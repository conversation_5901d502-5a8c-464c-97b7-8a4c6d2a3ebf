// About Section JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Animate about section elements on scroll
    const observerOptions = { root: null, rootMargin: '0px', threshold: 0.1 };
    const observer = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);
    
    document.querySelectorAll('.about-image, .about-text, .values-list li, .stat-item').forEach(element => {
        element.classList.add('hidden');
        observer.observe(element);
    });
    
    // Counter animation for stats
    function animateCounter(element, target, duration) {
        let start = 0;
        const increment = target / (duration / 16); // 16ms is roughly 60fps
        
        function updateCounter() {
            start += increment;
            if (start >= target) {
                element.textContent = target;
                return;
            }
            
            element.textContent = Math.floor(start);
            requestAnimationFrame(updateCounter);
        }
        
        updateCounter();
    }
    
    // Start counter animation when stats are in view
    const statsObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const statNumber = entry.target.querySelector('.stat-number');
                if (statNumber) {
                    const target = parseInt(statNumber.getAttribute('data-count') || statNumber.textContent, 10);
                    animateCounter(statNumber, target, 2000); // 2000ms duration
                }
                statsObserver.unobserve(entry.target);
            }
        });
    }, { threshold: 0.5 });
    
    document.querySelectorAll('.stat-item').forEach(stat => {
        statsObserver.observe(stat);
    });
});
