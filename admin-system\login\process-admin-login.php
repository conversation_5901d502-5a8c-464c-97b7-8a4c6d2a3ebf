<?php
session_start();
include('../../php/config/db_config.php');

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

$email = filter_input(INPUT_POST, 'email', FILTER_SANITIZE_EMAIL);
$password = $_POST['password'];

if (empty($email) || empty($password)) {
    echo json_encode(['success' => false, 'message' => 'Please fill in all fields']);
    exit;
}

try {
    // Check if admin exists
    $stmt = $con->prepare("SELECT * FROM admins WHERE email = ?");
    if (!$stmt) {
        throw new Exception('Database prepare failed: ' . $con->error);
    }
    
    $stmt->bind_param("s", $email);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        echo json_encode(['success' => false, 'message' => 'Invalid email or password']);
        exit;
    }
    
    $admin = $result->fetch_assoc();
    
    // Verify password
    if (password_verify($password, $admin['password'])) {
        // Set session variables
        $_SESSION['aid'] = $admin['admin_id'];
        $_SESSION['admin_name'] = $admin['first_name'] . ' ' . $admin['last_name'];
        $_SESSION['admin_email'] = $admin['email'];
        
        echo json_encode(['success' => true, 'message' => 'Login successful']);
    } else {
        echo json_encode(['success' => false, 'message' => 'Invalid email or password']);
    }
    
} catch (Exception $e) {
    error_log('Login error: ' . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'An error occurred. Please try again.']);
}

// Close the database connection
if (isset($stmt)) {
    $stmt->close();
}
if (isset($con)) {
    $con->close();
}
?> 