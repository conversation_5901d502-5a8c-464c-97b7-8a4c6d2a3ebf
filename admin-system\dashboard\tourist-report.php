<?php
session_start();
include('includes/config.php');

if(strlen($_SESSION['aid'])==0) {
    header('location:../login/admin-login.php');
    exit();
}

// Default to current month and year
$current_month = date('m');
$current_year = date('Y');

// Get month and year from POST if submitted
$selected_month = isset($_POST['month']) ? $_POST['month'] : $current_month;
$selected_year = isset($_POST['year']) ? $_POST['year'] : $current_year;
$report_type = isset($_POST['report_type']) ? $_POST['report_type'] : 'month';

// Initialize variables for date range
$from_date = '';
$to_date = '';

// Set date range based on report type
if ($report_type == 'month') {
    // For specific month
    $from_date = $selected_year . '-' . $selected_month . '-01';
    $to_date = date('Y-m-t', strtotime($from_date)); // Last day of the month
} elseif ($report_type == 'year') {
    // For whole year
    $from_date = $selected_year . '-01-01';
    $to_date = $selected_year . '-12-31';
}

// Get statistics
$total_tourists = 0;
$male_tourists = 0;
$female_tourists = 0;

if (!empty($from_date) && !empty($to_date)) {
    $sql = "SELECT
        COUNT(*) as total,
        SUM(CASE WHEN b.sex = 'male' THEN 1 ELSE 0 END) as male,
        SUM(CASE WHEN b.sex = 'female' THEN 1 ELSE 0 END) as female
    FROM bookings b
    WHERE DATE(b.booking_time) BETWEEN ? AND ?";
    $stmt = $con->prepare($sql);
    $stmt->bind_param("ss", $from_date, $to_date);
    $stmt->execute();
    $stats_query = $stmt->get_result();

    if($stats_query) {
        $stats = mysqli_fetch_assoc($stats_query);
        $total_tourists = $stats['total'] ?? 0;
        $male_tourists = $stats['male'] ?? 0;
        $female_tourists = $stats['female'] ?? 0;
    }
}

// Month names for dropdown
$month_names = [
    '01' => 'January',
    '02' => 'February',
    '03' => 'March',
    '04' => 'April',
    '05' => 'May',
    '06' => 'June',
    '07' => 'July',
    '08' => 'August',
    '09' => 'September',
    '10' => 'October',
    '11' => 'November',
    '12' => 'December'
];

// Get years for dropdown (current year and 5 years back)
$years = [];
for ($i = 0; $i <= 5; $i++) {
    $years[] = date('Y', strtotime("-$i years"));
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Tourist Report | Boat Booking System</title>
  <link rel="stylesheet" href="plugins/fontawesome-free/css/all.min.css">
  <link rel="stylesheet" href="dist/css/adminlte.min.css">
  <link rel="stylesheet" href="plugins/datatables-bs4/css/dataTables.bootstrap4.min.css">
  <link rel="stylesheet" href="plugins/datatables-responsive/css/responsive.bootstrap4.min.css">
  <link rel="stylesheet" href="plugins/datatables-buttons/css/buttons.bootstrap4.min.css">
  <style>
    @media print {
      .no-print {
        display: none;
      }
      .print-only {
        display: block !important;
      }
    }
    .print-only {
      display: none;
    }
  </style>
</head>
<body class="hold-transition sidebar-mini">
<div class="wrapper">
  <?php include_once('includes/navbar.php'); ?>
  <?php include_once('includes/sidebar.php'); ?>

  <div class="content-wrapper">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6">
            <h1>Tourist Report</h1>
          </div>
        </div>
      </div>
    </section>

    <section class="content">
      <div class="container-fluid">
        <div class="row">
          <div class="col-12">
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">Generate Tourist Report</h3>
              </div>
              <div class="card-body">
                <form method="post" action="" id="reportForm">
                  <div class="row">
                    <div class="col-md-3">
                      <div class="form-group">
                        <label>Report Type</label>
                        <select class="form-control" name="report_type" id="report_type">
                          <option value="month" <?php echo ($report_type == 'month') ? 'selected' : ''; ?>>Monthly Report</option>
                          <option value="year" <?php echo ($report_type == 'year') ? 'selected' : ''; ?>>Yearly Report</option>
                        </select>
                      </div>
                    </div>
                    <div class="col-md-3 month-selector" <?php echo ($report_type == 'year') ? 'style="display:none;"' : ''; ?>>
                      <div class="form-group">
                        <label>Month</label>
                        <select class="form-control" name="month">
                          <?php foreach ($month_names as $value => $name): ?>
                            <option value="<?php echo $value; ?>" <?php echo ($selected_month == $value) ? 'selected' : ''; ?>>
                              <?php echo $name; ?>
                            </option>
                          <?php endforeach; ?>
                        </select>
                      </div>
                    </div>
                    <div class="col-md-3">
                      <div class="form-group">
                        <label>Year</label>
                        <select class="form-control" name="year">
                          <?php foreach ($years as $year): ?>
                            <option value="<?php echo $year; ?>" <?php echo ($selected_year == $year) ? 'selected' : ''; ?>>
                              <?php echo $year; ?>
                            </option>
                          <?php endforeach; ?>
                        </select>
                      </div>
                    </div>
                    <div class="col-md-3">
                      <div class="form-group" style="margin-top: 32px;">
                        <button type="submit" class="btn btn-primary">
                          <i class="fas fa-search"></i> Generate Report
                        </button>
                        <?php if (!empty($from_date) && !empty($to_date)): ?>
                          <button type="button" class="btn btn-success" onclick="printReport()">
                            <i class="fas fa-print"></i> Print
                          </button>
                        <?php endif; ?>
                      </div>
                    </div>
                  </div>
                </form>

                <?php if (!empty($from_date) && !empty($to_date)): ?>
                  <div class="row mt-4">
                    <div class="col-md-4">
                      <div class="info-box">
                        <span class="info-box-icon bg-info"><i class="fas fa-users"></i></span>
                        <div class="info-box-content">
                          <span class="info-box-text">Total Tourists</span>
                          <span class="info-box-number"><?php echo $total_tourists; ?></span>
                        </div>
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="info-box">
                        <span class="info-box-icon bg-primary"><i class="fas fa-male"></i></span>
                        <div class="info-box-content">
                          <span class="info-box-text">Male Tourists</span>
                          <span class="info-box-number"><?php echo $male_tourists; ?></span>
                        </div>
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="info-box">
                        <span class="info-box-icon bg-danger"><i class="fas fa-female"></i></span>
                        <div class="info-box-content">
                          <span class="info-box-text">Female Tourists</span>
                          <span class="info-box-number"><?php echo $female_tourists; ?></span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="table-responsive mt-4">
                    <table class="table table-bordered table-striped" id="reportTable">
                      <thead>
                        <tr>
                          <th>#</th>
                          <th>Booking ID</th>
                          <th>Tourist Name</th>
                          <th>Age</th>
                          <th>Sex</th>
                          <th>Contact Number</th>
                          <th>Email</th>
                          <th>Address</th>
                          <th>Destination</th>
                          <th>Booking Date</th>
                        </tr>
                      </thead>
                      <tbody>
                        <?php
                        $sql = "SELECT
                            b.booking_id,
                            b.booking_code,
                            b.first_name,
                            b.last_name,
                            b.age,
                            b.sex,
                            b.contact_number,
                            b.email,
                            b.address,
                            d.name as destination_name,
                            b.booking_time
                        FROM bookings b
                        LEFT JOIN destinations d ON b.destination_id = d.destination_id
                        WHERE DATE(b.booking_time) BETWEEN ? AND ?
                        ORDER BY b.booking_time DESC";
                        $stmt = $con->prepare($sql);
                        $stmt->bind_param("ss", $from_date, $to_date);
                        $stmt->execute();
                        $query = $stmt->get_result();

                        if($query) {
                            $cnt = 1;
                            while($row = mysqli_fetch_assoc($query)) {
                                echo "<tr>";
                                echo "<td>".$cnt++."</td>";
                                echo "<td>".htmlspecialchars($row['booking_code'])."</td>";
                                echo "<td>".htmlspecialchars($row['first_name'].' '.$row['last_name'])."</td>";
                                echo "<td>".htmlspecialchars($row['age'])."</td>";
                                echo "<td>".htmlspecialchars(ucfirst($row['sex']))."</td>";
                                echo "<td>".htmlspecialchars($row['contact_number'])."</td>";
                                echo "<td>".htmlspecialchars($row['email'])."</td>";
                                echo "<td>".htmlspecialchars($row['address'])."</td>";
                                echo "<td>".htmlspecialchars($row['destination_name'])."</td>";
                                echo "<td>".date('M d, Y', strtotime($row['booking_time']))."</td>";
                                echo "</tr>";
                            }
                        }
                        ?>
                      </tbody>
                    </table>
                  </div>
                <?php endif; ?>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>

  <?php include_once('includes/footer.php'); ?>
</div>

<script src="plugins/jquery/jquery.min.js"></script>
<script src="plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
<script src="dist/js/adminlte.min.js"></script>
<script src="plugins/datatables/jquery.dataTables.min.js"></script>
<script src="plugins/datatables-bs4/js/dataTables.bootstrap4.min.js"></script>
<script src="plugins/datatables-responsive/js/responsive.bootstrap4.min.js"></script>
<script src="plugins/datatables-buttons/js/buttons.bootstrap4.min.js"></script>
<script>
$(function () {
    // Check if DataTable is already initialized
    if ($.fn.dataTable.isDataTable('#reportTable')) {
        // If already initialized, destroy it first
        $('#reportTable').DataTable().destroy();
    }

    // Initialize DataTable with proper configuration
    $('#reportTable').DataTable({
        "paging": true,
        "lengthChange": false,
        "searching": true,
        "ordering": true,
        "info": true,
        "autoWidth": false,
        "responsive": true,
    });

    // Toggle month selector based on report type
    $('#report_type').change(function() {
        if ($(this).val() === 'year') {
            $('.month-selector').hide();
        } else {
            $('.month-selector').show();
        }
    });
});

function printReport() {
    var printWindow = window.open('', '_blank');
    printWindow.document.write('<html><head><title>Tourist Report</title>');
    printWindow.document.write('<link rel="stylesheet" href="plugins/bootstrap/css/bootstrap.min.css">');
    printWindow.document.write('<style>@media print { .no-print { display: none; } }</style>');
    printWindow.document.write('</head><body>');

    // Add report header
    printWindow.document.write('<div class="container mt-4">');
    printWindow.document.write('<h2 class="text-center">Tourist Report</h2>');

    // Add report period
    <?php if ($report_type == 'month'): ?>
    printWindow.document.write('<p class="text-center">Month: <?php echo $month_names[$selected_month] . ' ' . $selected_year; ?></p>');
    <?php else: ?>
    printWindow.document.write('<p class="text-center">Year: <?php echo $selected_year; ?></p>');
    <?php endif; ?>

    // Add summary statistics
    printWindow.document.write('<div class="row mt-4 mb-4">');
    printWindow.document.write('<div class="col-md-4 text-center"><strong>Total Tourists:</strong> <?php echo $total_tourists; ?></div>');
    printWindow.document.write('<div class="col-md-4 text-center"><strong>Male Tourists:</strong> <?php echo $male_tourists; ?></div>');
    printWindow.document.write('<div class="col-md-4 text-center"><strong>Female Tourists:</strong> <?php echo $female_tourists; ?></div>');
    printWindow.document.write('</div>');

    // Add table
    printWindow.document.write('<table class="table table-bordered">');
    printWindow.document.write('<thead><tr><th>#</th><th>Tourist Name</th><th>Age</th><th>Sex</th><th>Contact Number</th><th>Email</th><th>Address</th><th>Destination</th><th>Booking Date</th></tr></thead>');
    printWindow.document.write('<tbody>');

    // Add table rows
    <?php
    if (!empty($from_date) && !empty($to_date)) {
        $sql = "SELECT
            b.booking_id,
            b.booking_code,
            b.first_name,
            b.last_name,
            b.age,
            b.sex,
            b.contact_number,
            b.email,
            b.address,
            d.name as destination_name,
            b.booking_time
        FROM bookings b
        LEFT JOIN destinations d ON b.destination_id = d.destination_id
        WHERE DATE(b.booking_time) BETWEEN ? AND ?
        ORDER BY b.booking_time DESC";
        $stmt = $con->prepare($sql);
        $stmt->bind_param("ss", $from_date, $to_date);
        $stmt->execute();
        $query = $stmt->get_result();

        if($query) {
            $cnt = 1;
            while($row = mysqli_fetch_assoc($query)) {
                echo "printWindow.document.write('<tr>');\n";
                echo "printWindow.document.write('<td>".$cnt++."</td>');\n";
                echo "printWindow.document.write('<td>".htmlspecialchars($row['first_name'].' '.$row['last_name'])."</td>');\n";
                echo "printWindow.document.write('<td>".htmlspecialchars($row['age'])."</td>');\n";
                echo "printWindow.document.write('<td>".htmlspecialchars(ucfirst($row['sex']))."</td>');\n";
                echo "printWindow.document.write('<td>".htmlspecialchars($row['contact_number'])."</td>');\n";
                echo "printWindow.document.write('<td>".htmlspecialchars($row['email'])."</td>');\n";
                echo "printWindow.document.write('<td>".htmlspecialchars($row['address'])."</td>');\n";
                echo "printWindow.document.write('<td>".htmlspecialchars($row['destination_name'])."</td>');\n";
                echo "printWindow.document.write('<td>".date('M d, Y', strtotime($row['booking_time']))."</td>');\n";
                echo "printWindow.document.write('</tr>');\n";
            }
        }
    }
    ?>

    printWindow.document.write('</tbody></table>');

    // Add footer
    printWindow.document.write('<div class="text-center mt-4">');
    printWindow.document.write('<p>Generated on: <?php echo date("F d, Y h:i A"); ?></p>');
    printWindow.document.write('<p>Municipality of Carles Tourism Office</p>');
    printWindow.document.write('</div>');

    printWindow.document.write('</div>');
    printWindow.document.write('</body></html>');

    printWindow.document.close();
    printWindow.focus();

    // Add a slight delay before printing to ensure content is loaded
    setTimeout(function() {
        printWindow.print();
    }, 500);
}
</script>
</body>
</html>
