<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session
session_start();

// Include database configuration
include('includes/config.php');
include('debug_log.php');

// Check if admin is logged in
if (!isset($_SESSION['aid']) || empty($_SESSION['aid'])) {
    header('location:../login/admin-login.php');
    exit();
}

// Check if it's a POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'error' => 'Invalid request method']);
    exit;
}

// Get and validate input
$booking_id = isset($_POST['id']) ? $_POST['id'] : '';

// Log the raw booking ID for debugging
debug_log("Processing booking details request", ['raw_booking_id' => $booking_id, 'post_data' => $_POST]);

// Check if booking ID is provided and is numeric
if (!isset($_POST['id']) || $_POST['id'] === '') {
    log_booking_id_issue($booking_id, "Missing booking ID", ['post_data' => $_POST]);
    echo json_encode(['success' => false, 'error' => 'Please provide a booking ID']);
    exit;
}

if (!is_numeric($_POST['id'])) {
    log_booking_id_issue($booking_id, "Non-numeric booking ID", ['post_data' => $_POST]);
    echo json_encode(['success' => false, 'error' => 'Booking ID must be a number']);
    exit;
}

// Convert to integer after validation
$booking_id = intval($_POST['id']);

if ($booking_id <= 0) {
    log_booking_id_issue($booking_id, "Invalid booking ID value", ['post_data' => $_POST]);
    echo json_encode(['success' => false, 'error' => 'Invalid booking ID: Value must be a positive number']);
    exit;
}

try {
    // Test database connection
    if (!$con) {
        throw new Exception('Database connection failed: ' . mysqli_connect_error());
    }

    // First check if the booking exists
    $check_query = "SELECT booking_id FROM bookings WHERE booking_id = ?";
    $check_stmt = $con->prepare($check_query);

    if (!$check_stmt) {
        throw new Exception('Database prepare failed for check query: ' . $con->error);
    }

    $check_stmt->bind_param("i", $booking_id);
    $check_stmt->execute();
    $check_result = $check_stmt->get_result();

    if ($check_result->num_rows === 0) {
        log_booking_id_issue($booking_id, "Booking ID does not exist in database");
        echo json_encode(['success' => false, 'error' => 'Booking ID ' . $booking_id . ' does not exist in the database']);
        $check_stmt->close();
        exit;
    }

    debug_log("Booking found in database", ['booking_id' => $booking_id]);

    $check_stmt->close();

    // Get booking details with related information
    // Check if required columns exist in the bookings table
    $check_columns = [
        'drop_off_location' => false,
        'emergency_name' => false,
        'emergency_number' => false
    ];

    foreach (array_keys($check_columns) as $column) {
        $check_column_query = "SHOW COLUMNS FROM bookings LIKE '$column'";
        $check_column_result = $con->query($check_column_query);
        $check_columns[$column] = $check_column_result->num_rows > 0;
    }

    // Build the query based on which columns exist
    $query = "SELECT
        b.*,
        COALESCE(c.first_name, b.first_name) as first_name,
        COALESCE(c.last_name, b.last_name) as last_name,
        COALESCE(c.email, b.email) as email,
        COALESCE(c.contact_number, b.contact_number) as contact_number,
        COALESCE(c.address, b.address) as address,
        COALESCE(c.age, b.age) as age,
        COALESCE(c.sex, b.sex) as sex,";

    // Add emergency_name only if the column exists
    if ($check_columns['emergency_name']) {
        $query .= "
        IFNULL(b.emergency_name, '') as emergency_name,";
    } else {
        $query .= "
        '' as emergency_name,";
    }

    // Add emergency_number only if the column exists
    if ($check_columns['emergency_number']) {
        $query .= "
        IFNULL(b.emergency_number, '') as emergency_number,";
    } else {
        $query .= "
        '' as emergency_number,";
    }

    // Add drop_off_location only if the column exists
    if ($check_columns['drop_off_location']) {
        $query .= "
        IFNULL(b.drop_off_location, '') as drop_off_location,";
    } else {
        $query .= "
        '' as drop_off_location,";
    }

    $query .= "
        bt.name as boat_name,
        bt.price_per_day,
        d.name as destination_name
    FROM bookings b
    LEFT JOIN customers c ON b.customer_id = c.customer_id
    LEFT JOIN boats bt ON b.boat_id = bt.boat_id
    LEFT JOIN destinations d ON b.destination_id = d.destination_id
    WHERE b.booking_id = ?";

    $stmt = $con->prepare($query);

    if (!$stmt) {
        throw new Exception('Database prepare failed: ' . $con->error);
    }

    $stmt->bind_param("i", $booking_id);

    if (!$stmt->execute()) {
        throw new Exception('Database execute failed: ' . $stmt->error);
    }

    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        // Log the issue for debugging
        error_log("Booking not found in database: ID = " . $booking_id);

        // Check if the booking exists at all (without joins)
        $check_query = "SELECT booking_id FROM bookings WHERE booking_id = ?";
        $check_stmt = $con->prepare($check_query);

        if (!$check_stmt) {
            throw new Exception('Database prepare failed for check query: ' . $con->error);
        }

        $check_stmt->bind_param("i", $booking_id);
        $check_stmt->execute();
        $check_result = $check_stmt->get_result();

        if ($check_result->num_rows === 0) {
            echo json_encode(['success' => false, 'error' => 'Booking ID ' . $booking_id . ' does not exist in the database']);
        } else {
            echo json_encode(['success' => false, 'error' => 'Booking found but related data is missing (customer, boat, or destination)']);
        }

        $check_stmt->close();
        exit;
    }

    $booking = $result->fetch_assoc();

    // Format dates with better error handling
    try {
        if (!empty($booking['start_date']) && $booking['start_date'] != '0000-00-00 00:00:00') {
            $start_timestamp = strtotime($booking['start_date']);
            if ($start_timestamp !== false) {
                $booking['start_date'] = date('F d, Y', $start_timestamp);
            } else {
                $booking['start_date'] = 'Not Available';
            }
        } else {
            $booking['start_date'] = 'Not Available';
        }
    } catch (Exception $e) {
        error_log("Error formatting start_date: " . $e->getMessage());
        $booking['start_date'] = 'Not Available';
    }

    try {
        if (!empty($booking['end_date']) && $booking['end_date'] != '0000-00-00 00:00:00') {
            $end_timestamp = strtotime($booking['end_date']);
            if ($end_timestamp !== false) {
                $booking['end_date'] = date('F d, Y', $end_timestamp);
            } else {
                $booking['end_date'] = 'Not Available';
            }
        } else {
            $booking['end_date'] = 'Not Available';
        }
    } catch (Exception $e) {
        error_log("Error formatting end_date: " . $e->getMessage());
        $booking['end_date'] = 'Not Available';
    }

    try {
        if (!empty($booking['booking_time']) && $booking['booking_time'] != '00:00:00') {
            $time_timestamp = strtotime($booking['booking_time']);
            if ($time_timestamp !== false) {
                $booking['booking_time'] = date('F d, Y \a\t h:i A', $time_timestamp);
            } else {
                $booking['booking_time'] = 'Not Available';
            }
        } else {
            $booking['booking_time'] = 'Not Available';
        }
    } catch (Exception $e) {
        error_log("Error formatting booking_time: " . $e->getMessage());
        $booking['booking_time'] = 'Not Available';
    }

    // Format currency values - ensure they are treated as numbers
    $booking['environmental_fee'] = floatval($booking['environmental_fee']);
    $booking['total'] = floatval($booking['total']);
    $booking['price_per_day'] = floatval($booking['price_per_day']);

    // Calculate duration if both dates are valid
    if ($booking['start_date'] != 'N/A' && $booking['end_date'] != 'N/A') {
        $start = strtotime($booking['start_date']);
        $end = strtotime($booking['end_date']);
        $duration = $end - $start;
        $days = floor($duration / (24 * 60 * 60));
        $hours = floor(($duration % (24 * 60 * 60)) / (60 * 60));
        $minutes = floor(($duration % (60 * 60)) / 60);
        $durationStr = '';
        if($days > 0) $durationStr .= $days . ' days ';
        if($hours > 0) $durationStr .= $hours . ' hours ';
        if($minutes > 0) $durationStr .= $minutes . ' minutes';
        $booking['duration'] = trim($durationStr) ?: 'Not set';
    } else {
        $booking['duration'] = 'Not set';
    }

    // Format status
    $booking['status_display'] = ucfirst($booking['booking_status'] ?? 'pending');
    $booking['status_class'] = ($booking['booking_status'] == 'cancelled') ? 'danger' :
                             (($booking['booking_status'] == 'confirmed') ? 'success' : 'warning');

    // Clean up null values and set default values for important fields
    foreach ($booking as $key => $value) {
        if ($value === null || $value === '' || $value === 'N/A') {
            // Set specific default values for important fields
            if ($key === 'destination_name') {
                $booking[$key] = 'Gigantes Island';
            } else if ($key === 'boat_name') {
                $booking[$key] = 'Assigned by Tourism Office';
            } else if ($key === 'total' && (empty($value) || $value == 0)) {
                // Calculate total based on number of pax if it's zero
                $pax = intval($booking['no_of_pax'] ?? 1);
                $booking[$key] = $pax * 75; // Default calculation: regular fee per person
            } else if ($key === 'age') {
                $booking[$key] = '25'; // Default age
            } else if ($key === 'sex') {
                $booking[$key] = 'Not Specified'; // Default sex
            } else if ($key === 'drop_off_location') {
                $booking[$key] = 'Estancia'; // Default drop-off location
            } else if ($key === 'no_of_pax') {
                $booking[$key] = '1'; // Default number of pax
            } else if ($key === 'environmental_fee') {
                $booking[$key] = '75'; // Default environmental fee
            } else if ($key === 'payment_method') {
                $booking[$key] = 'Cash'; // Default payment method
            } else if ($key === 'emergency_name') {
                $booking[$key] = 'Emergency Contact'; // Default emergency contact name
            } else if ($key === 'emergency_number') {
                $booking[$key] = '09123456789'; // Default emergency contact number
            } else if ($key === 'booking_status') {
                $booking[$key] = 'pending'; // Default booking status
            } else {
                $booking[$key] = 'Not Available';
            }
        }
    }

    // Make sure destination is properly set
    if (empty($booking['destination_name']) || $booking['destination_name'] === 'Not Available') {
        $booking['destination_name'] = 'Gigantes Island';
    }

    // Make sure boat is properly set
    if (empty($booking['boat_name']) || $booking['boat_name'] === 'Not Available') {
        $booking['boat_name'] = 'Assigned by Tourism Office';
    }

    // Make sure total is properly set
    if (empty($booking['total']) || $booking['total'] <= 0) {
        $pax = intval($booking['no_of_pax'] ?? 1);
        $booking['total'] = $pax * 75; // Default calculation: regular fee per person
    }

    // Make sure age is properly set
    if (empty($booking['age']) || $booking['age'] === 'Not Available') {
        $booking['age'] = '25';
    }

    // Make sure sex is properly set
    if (empty($booking['sex']) || $booking['sex'] === 'Not Available') {
        $booking['sex'] = 'Not Specified';
    }

    // Make sure drop_off_location is properly set
    if (empty($booking['drop_off_location']) || $booking['drop_off_location'] === 'Not Available') {
        $booking['drop_off_location'] = 'Estancia';
    }

    // Make sure no_of_pax is properly set
    if (empty($booking['no_of_pax']) || $booking['no_of_pax'] === 'Not Available' || intval($booking['no_of_pax']) <= 0) {
        $booking['no_of_pax'] = '1';
    }

    // Make sure environmental_fee is properly set
    if (empty($booking['environmental_fee']) || $booking['environmental_fee'] === 'Not Available' || floatval($booking['environmental_fee']) <= 0) {
        $booking['environmental_fee'] = '75';
    }

    // Make sure payment_method is properly set
    if (empty($booking['payment_method']) || $booking['payment_method'] === 'Not Available') {
        $booking['payment_method'] = 'Cash';
    }

    // Make sure emergency_name is properly set
    if (empty($booking['emergency_name']) || $booking['emergency_name'] === 'Not Available') {
        $booking['emergency_name'] = 'Emergency Contact';
    }

    // Make sure emergency_number is properly set
    if (empty($booking['emergency_number']) || $booking['emergency_number'] === 'Not Available') {
        $booking['emergency_number'] = '09123456789';
    }

    // Make sure booking_status is properly set
    if (empty($booking['booking_status']) || $booking['booking_status'] === 'Not Available') {
        $booking['booking_status'] = 'pending';
    }

    // Log successful retrieval
    debug_log("Successfully retrieved booking details", [
        'booking_id' => $booking_id,
        'booking_code' => $booking['booking_code'] ?? 'N/A'
    ]);

    echo json_encode([
        'success' => true,
        'data' => $booking
    ]);

} catch (Exception $e) {
    log_db_error('get-booking-details.php', $e->getMessage(), ['booking_id' => $booking_id]);
    echo json_encode([
        'success' => false,
        'error' => 'Database error: ' . $e->getMessage()
    ]);
}

// Close the database connection
if (isset($stmt)) {
    $stmt->close();
}
if (isset($con)) {
    $con->close();
}

function calculateDuration($startDate, $endDate) {
    $start = strtotime($startDate);
    $end = strtotime($endDate);
    $duration = $end - $start;
    $days = floor($duration / (24 * 60 * 60));
    $hours = floor(($duration % (24 * 60 * 60)) / (60 * 60));
    $minutes = floor(($duration % (60 * 60)) / 60);
    $durationStr = $days > 0 ? "$days days" : "";
    $durationStr .= $hours > 0 ? " $hours hours" : "";
    $durationStr .= $minutes > 0 ? " $minutes minutes" : "";
    return trim($durationStr) ?: 'N/A';
}

exit();
?>
