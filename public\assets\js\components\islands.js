// Islands Section JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // --- Island Modal (Explore/VIEW) ---
    function setupIslandModal(btnSelector, cardSelector) {
        document.querySelectorAll(btnSelector).forEach(function(btn) {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                const card = btn.closest(cardSelector);
                if (!card) return;

                // Get the island name and convert to lowercase with hyphens
                let name = card.querySelector('h3').textContent.trim();
                console.log('Original name before processing:', name);

                // Direct mapping for specific island names
                if (name === 'GIGANTES NORTE') {
                    name = 'gigantes-norte';
                } else if (name === 'GIGANTES SUR') {
                    name = 'gigantes-sur';
                } else if (name === 'SICOGON') {
                    name = 'sicogon-island';
                } else {
                    // Default processing for other names
                    name = name.toLowerCase().replace(/\s+/g, '-');
                }

                console.log('Processed name:', name);

                const infoId = name + '-info';
                console.log('Looking for island info with ID:', infoId);

                const infoDiv = document.getElementById(infoId);
                const modal = document.getElementById('islandModal');
                const modalBody = document.getElementById('islandModalBody');

                if (infoDiv && modal && modalBody) {
                    console.log('Found island info, displaying modal');
                    modalBody.innerHTML = infoDiv.innerHTML;
                    modal.style.display = 'flex';
                    document.body.style.overflow = 'hidden';
                } else {
                    console.error('Could not find island info with ID:', infoId);
                    if (!infoDiv) console.error('infoDiv not found');
                    if (!modal) console.error('modal not found');
                    if (!modalBody) console.error('modalBody not found');
                }
            });
        });
    }

    setupIslandModal('.island-card .btn-explore', '.island-card');
    setupIslandModal('.island-card-small .view-btn', '.island-card-small');

    const closeIslandModal = () => {
        document.getElementById('islandModal').style.display = 'none';
        document.body.style.overflow = 'auto';
    };

    const closeIslandBtn = document.getElementById('closeIslandModal');
    if (closeIslandBtn) closeIslandBtn.addEventListener('click', closeIslandModal);

    const islandModalBg = document.querySelector('.island-modal-bg');
    if (islandModalBg) islandModalBg.addEventListener('click', closeIslandModal);

    // Animate island cards on scroll
    const observerOptions = { root: null, rootMargin: '0px', threshold: 0.1 };
    const observer = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);

    document.querySelectorAll('.island-card, .island-card-small').forEach(element => {
        element.classList.add('hidden');
        observer.observe(element);
    });

    // Make sure the fixed list is properly initialized
    const fixedList = document.querySelector('.island-fixed-list');
    if (fixedList) {
        console.log('Fixed island list found, ensuring proper display');
        fixedList.style.display = 'flex';
        fixedList.style.flexDirection = 'column';
    }
});
