<?php
session_start();
include('includes/config.php');
if(strlen($_SESSION['aid'])==0) {
  header('location:../login/admin-login.php');
  exit();
}

$booking = null;
$msg = '';
if (isset($_GET['id'])) {
    $id = intval($_GET['id']);
    $stmt = $con->prepare("SELECT * FROM bookings WHERE booking_id=? LIMIT 1");
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $result = $stmt->get_result();
    $booking = $result->fetch_assoc();
    if (!$booking) {
        $msg = 'Booking not found.';
    }
    $stmt->close();
} else {
    $msg = 'No booking ID provided.';
}

// Handle form submission
if (isset($_POST['update'])) {
    $status = $_POST['booking_status'];
    $start_date = $_POST['start_date'];
    $end_date = $_POST['end_date'];
    $stmt = $con->prepare("UPDATE bookings SET booking_status=?, start_date=?, end_date=? WHERE booking_id=?");
    $stmt->bind_param("sssi", $status, $start_date, $end_date, $id);
    $result = $stmt->execute();
    if ($result) {
        // Send notification email based on status
        if ($status == 'confirmed') {
            // Send confirmation email
            $ch = curl_init('http://localhost/Online%20Booking%20Reservation%20System/process/process_booking_verified_notification.php');
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, ['booking_id' => $id]);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_exec($ch);
            curl_close($ch);
            // Redirect to accepted bookings
            header('Location: accepted-bookings.php');
            exit();
        } elseif ($status == 'cancelled') {
            // Send rejection email
            $ch = curl_init('http://localhost/Online%20Booking%20Reservation%20System/process/process_booking_rejected_notification.php');
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, ['booking_id' => $id]);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_exec($ch);
            curl_close($ch);
            // Redirect to rejected bookings
            header('Location: rejected-bookings.php');
            exit();
        } elseif ($status == 'pending') {
            // Redirect to pending bookings
            header('Location: pending-bookings.php');
            exit();
        }
        $msg = 'Booking updated successfully!';
        // Refresh booking data
        $stmt = $con->prepare("SELECT * FROM bookings WHERE booking_id=? LIMIT 1");
        $stmt->bind_param("i", $id);
        $stmt->execute();
        $result = $stmt->get_result();
        $booking = $result->fetch_assoc();
        $stmt->close();
    } else {
        $msg = 'Update failed. Please try again.';
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Edit Booking</title>
  <link rel="stylesheet" href="plugins/fontawesome-free/css/all.min.css">
  <link rel="stylesheet" href="dist/css/adminlte.min.css">
</head>
<body class="hold-transition sidebar-mini">
<div class="wrapper">
<?php include_once("includes/navbar.php");?>
<?php include_once("includes/sidebar.php");?>
<div class="content-wrapper">
  <section class="content-header">
    <div class="container-fluid">
      <div class="row mb-2">
        <div class="col-sm-6">
          <h1>Edit Booking</h1>
        </div>
      </div>
    </div>
  </section>
  <section class="content">
    <div class="container-fluid">
      <?php if($msg) echo '<div class="alert alert-info">'.htmlspecialchars($msg).'</div>'; ?>
      <?php if($booking): ?>
      <form method="post">
        <div class="form-group">
          <label>Status</label>
          <select name="booking_status" class="form-control" required>
            <option value="confirmed" <?php if($booking['booking_status']=='confirmed' || $booking['booking_status']=='Confirmed') echo 'selected'; ?>>Confirmed</option>
            <option value="pending" <?php if($booking['booking_status']=='pending' || $booking['booking_status']=='Pending') echo 'selected'; ?>>Pending</option>
            <option value="cancelled" <?php if($booking['booking_status']=='cancelled' || $booking['booking_status']=='Cancelled') echo 'selected'; ?>>Cancelled</option>
          </select>
        </div>
        <div class="form-group">
          <label>Start Date</label>
          <input type="date" name="start_date" class="form-control" value="<?php echo htmlspecialchars($booking['start_date']); ?>" required>
        </div>
        <div class="form-group">
          <label>End Date</label>
          <input type="date" name="end_date" class="form-control" value="<?php echo htmlspecialchars($booking['end_date']); ?>" required>
        </div>

        <button type="submit" name="update" class="btn btn-primary">Update Booking</button>
        <a href="all-reservations.php" class="btn btn-secondary">Back</a>
      </form>
      <?php endif; ?>
    </div>
  </section>
</div>
<?php include_once("includes/footer.php");?>
</div>
</body>
</html>
