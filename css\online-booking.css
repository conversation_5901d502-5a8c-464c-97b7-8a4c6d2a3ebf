/*CSS*/

/* Root Variables */
:root {
    /* Colors */
    --primary-color: #0f3460;
    --secondary-color: #3282b8;
    --accent-color: #1b998b;
    --text-color: #2C3E50;
    --light-color: #ffffff;
    --dark-color: #1A1A1A;

    /* Typography - Fluid font sizes using clamp */
    --fs-300: clamp(0.8rem, calc(0.75rem + 0.25vw), 0.9rem);
    --fs-400: clamp(0.9rem, calc(0.85rem + 0.3vw), 1.1rem);
    --fs-500: clamp(1.1rem, calc(1rem + 0.5vw), 1.3rem);
    --fs-600: clamp(1.4rem, calc(1.2rem + 1vw), 1.8rem);
    --fs-700: clamp(1.8rem, calc(1.5rem + 1.5vw), 2.5rem);
    --fs-800: clamp(2.2rem, calc(1.8rem + 2vw), 3.5rem);
    --fs-900: clamp(3rem, calc(2.5rem + 3vw), 5rem);

    /* Spacing - Fluid spacing using clamp */
    --space-xs: clamp(0.5rem, calc(0.5rem + 0.25vw), 0.75rem);
    --space-sm: clamp(0.75rem, calc(0.75rem + 0.5vw), 1rem);
    --space-md: clamp(1rem, calc(1rem + 1vw), 1.5rem);
    --space-lg: clamp(1.5rem, calc(1.5rem + 1.5vw), 2.5rem);
    --space-xl: clamp(2rem, calc(2rem + 2vw), 4rem);

    /* Transitions */
    --transition-fast: all 0.2s ease;
    --transition-normal: all 0.3s ease;
    --transition-slow: all 0.5s ease;

    /* Focus and Hover States */
    --focus-ring: 0 0 0 3px rgba(27, 153, 139, 0.3);
    --hover-scale: scale(1.05);
    --hover-lift: translateY(-2px);

    /* Other */
    --radius-small: 10px;
    --radius-medium: 20px;
    --radius-large: 30px;
    --shadow-light: 0 5px 15px rgba(0,0,0,0.1);
    --shadow-medium: 0 10px 30px rgba(0,0,0,0.2);

    /* Max width container */
    --container-max: 1200px;
    --container-narrow: 900px;
}

/* Debug styles at the top of the file */
body {
    position: relative;
}

.section-bg-sequence-1::after,
.section-bg-sequence-2::after,
.section-bg-sequence-3::after,
.section-bg-sequence-4::after,
.section-bg-sequence-5::after {
    content: attr(class);
    position: absolute;
    bottom: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.5);
    color: #fff;
    padding: 5px 10px;
    font-size: 12px;
    z-index: 1000;
}

/* Make transitions more obvious */
.section-bg-sequence-1,
.section-bg-sequence-2,
.section-bg-sequence-3,
.section-bg-sequence-4,
.section-bg-sequence-5 {
    transition: all 1s ease-in-out;
}

/* Force visible styles */
.section-bg-sequence-3.scrolled::before,
.section-bg-sequence-5.scrolled::before {
    opacity: 1 !important;
    transform: scale(1) !important;
}

/* Fixed/modified styles for the images */
.section-bg-sequence-1 {
    background-image: url('../../img/3.jpg') !important;
    background-size: cover !important;
    background-position: center !important;
    background-repeat: no-repeat !important;
    position: relative;
}

.section-bg-sequence-1::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    z-index: -1;
}

.section-bg-sequence-2 {
    background-color: #102030 !important;
    position: relative;
}

.section-bg-sequence-3 {
    background-image: url('../../img/bcground2.png') !important;
    background-size: cover !important;
    background-position: center !important;
    background-repeat: no-repeat !important;
    position: relative;
}

.section-bg-sequence-3::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    z-index: -1;
}

.section-bg-sequence-4 {
    background-color: #1e3350 !important;
    position: relative;
}

.section-bg-sequence-5 {
    background-image: url('../../img/bcground2.png') !important;
    background-size: cover !important;
    background-position: center !important;
    background-repeat: no-repeat !important;
    position: relative;
}

.section-bg-sequence-5::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    z-index: -1;
}

/* Background Image */
.background-image-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('../img/background system.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    z-index: -1;
}

/* Container sizing for better mobile experience */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

@media (max-width: 576px) {
    .container {
        width: 95%;
        padding: 0 10px;
    }
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    overflow-x: hidden;
    min-height: 100%;
    display: flex;
    flex-direction: column;
    background-color: transparent;
}

*,
*::before,
*::after {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Base Styles */
html {
    scroll-behavior: smooth;
    height: 100%;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    overflow-x: hidden;
    min-height: 100%;
    display: flex;
    flex-direction: column;
    background-color: transparent;
}

img {
    max-width: 100%;
    height: auto;
    display: block;
}

/* Utility Classes */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.visually-hidden {
    position: absolute !important;
    height: 1px;
    width: 1px;
    overflow: hidden;
    clip: rect(1px 1px 1px 1px);
    clip: rect(1px, 1px, 1px, 1px);
    white-space: nowrap;
}

/* Focus styles */
a:focus, button:focus {
    outline: 2px solid var(--accent-color);
    outline-offset: 2px;
}

a:focus-visible,
button:focus-visible {
    outline: 3px solid var(--accent-color);
    outline-offset: 3px;
}

/* Skip navigation for accessibility */
.skip-nav {
    background: var(--accent-color);
    color: white;
    padding: 0.5em 1.5em;
    position: absolute;
    transform: translateY(-100%);
    transition: transform 0.3s;
    left: 1em;
    z-index: 9999;
}

.skip-nav:focus {
    transform: translateY(0%);
}

/* Header & Navigations */
.site-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: transparent;
    transition: all 0.3s ease;
}

.main-nav {
    padding: 1rem 1.5rem;
    transition: all 0.3s ease;
}

.nav-container {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 0 2rem;
    justify-content: flex-start;
}

/* Left side - Logos of Carles Tourism */
.nav-logos {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    margin-right: auto;
}

.nav-logos img {
    height: 35px;
    width: auto;
}

.nav-link {
    display: flex;
    align-items: center;
    color: #fff;
    text-decoration: none;
    font-size: 0.85rem;
    padding: 0.25rem;
    margin-left: 0.25rem;
    transition: all 0.3s ease;
}

.nav-link:hover {
    color: #ffd700;
    transform: translateY(-2px);
}

.nav-link i {
    margin-right: 0.15rem;
}

.nav-link:last-child {
    background-color: #6c757d;
    color: #fff;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    font-weight: 600;
    margin-left: 0.5rem;
}

.nav-link:last-child:hover {
    background-color: #5a6268;
    color: #fff;
    transform: translateY(-2px);
}

.nav-logos .nav-link {
    display: flex;
    align-items: center;
    gap: 0.3rem;
    color: #fff;
    text-decoration: none;
    font-weight: 500;
    font-size: 0.9rem;
    padding: 0.3rem 0.8rem;
    transition: all 0.3s ease;
    margin-left: 1rem;
}

.nav-logos .nav-link i {
    font-size: 1rem;
}

.nav-logos .nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}
/* ===============================
// Main Navigation - Navbar
=============================== */
.nav-menu {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    margin-left: 2rem;
    flex: 1;
}

.nav-link {
    display: inline-flex;
    align-items: center;
    gap: 0.4rem;
    color: #fff;
    text-decoration: none;
    font-weight: 500;
    font-size: 0.95rem;
    padding: 0.6rem 1rem;
    border-radius: 5px;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.nav-link i {
    font-size: 1rem;
    margin-right: 0.2rem;
}

.nav-link:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
    color: #fff;
}

.nav-link.active {
    background: rgba(255, 255, 255, 0.2);
    font-weight: 600;
}

/* Right Navigation */
.nav-right {
    margin-left: auto;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.nav-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    color: #fff;
    text-decoration: none;
    font-weight: 500;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.contact-btn {
    background: rgba(108, 117, 125, 0.2);
}

.book-btn {
    background: rgba(40, 167, 69, 0.2);
}

.login-btn {
    background: rgba(0, 123, 255, 0.2);
}

.nav-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.nav-social {
    display: flex;
    gap: 0.5rem;
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    color: #fff;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transition: all 0.3s ease;
}

.social-link:hover {
    background: rgba(255, 255, 255, 0.2);
}

.nav-search-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    color: #fff;
    background: none;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
}

.nav-search-btn:hover {
    background: rgba(255, 255, 255, 0.1);
}

/* Scrolled State */
.site-header.scrolled {
    background: #fff;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.site-header.scrolled .nav-link,
.site-header.scrolled .nav-btn,
.site-header.scrolled .social-link,
.site-header.scrolled .nav-search-btn {
    color: #333;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .nav-container {
        gap: 1rem;
    }

    .nav-menu {
        gap: 1rem;
    }

    .nav-link {
        font-size: 0.9rem;
        padding: 0.5rem 0.8rem;
    }
}

@media (max-width: 768px) {
    .nav-container {
        justify-content: flex-start;
        padding: 0.5rem;
    }

    .nav-menu {
        display: none;
    }

    .nav-right {
        margin-left: auto;
    }

    .mobile-menu-toggle {
        display: block;
    }
}

@media (max-width: 768px) {
    .main-nav {
        padding: 0.5rem;
    }

    .nav-container {
        padding: 0.5rem;
    }

    .nav-logos {
        margin-right: auto;
    }

    .nav-logos img {
        height: 32px;
    }

    .nav-social {
        display: none;
    }
}

/* Transparent Navbar */
.site-header.transparent {
    background: transparent;
}

/* Mobile Menu Icon */
.menu-icon {
    display: block;
    width: 25px;
    height: 2px;
    background-color: currentColor;
    position: relative;
    transition: all 0.3s ease;
}

.menu-icon::before,
.menu-icon::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background-color: currentColor;
    transition: all 0.3s ease;
}

.menu-icon::before {
    top: -8px;
}

.menu-icon::after {
    bottom: -8px;
}

/* Navigation Right Section */
.nav-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.secondary-nav {
    display: flex;
    gap: 15px;
}

.secondary-nav a {
    color: #333;
    text-decoration: none;
    font-weight: 500;
    padding: 10px;
    transition: color 0.3s;
}

.secondary-nav a:hover {
    color: #007bff;
}

.social-icons {
    display: flex;
    gap: 15px;
}

.social-icons a {
    color: #333;
    font-size: 20px;
    transition: color 0.3s;
}

.social-icons a:hover {
    color: #007bff;
}

/* Mobile Menu */
@media (max-width: 991px) {
    .mobile-menu-toggle {
        display: block;
        background: none;
        border: none;
        padding: 10px;
        cursor: pointer;
    }

    .nav-links {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: #fff;
        padding: 20px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }

    .nav-links.active {
        display: block;
    }

    .nav-dropdown-menu {
        position: static;
        box-shadow: none;
        display: none;
    }

    .nav-dropdown.active .nav-dropdown-menu {
        display: block;
    }

    .nav-submenu {
        position: static;
        box-shadow: none;
        display: none;
    }

    .nav-dropdown-item:hover .nav-submenu {
        display: block;
    }
}

/* Dropdown Menus */
.nav-dropdown-menu {
        position: absolute;
        top: 100%;
    left: 0;
    background: #fff;
    min-width: 250px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    display: none;
    z-index: 1000;
}

.nav-dropdown:hover .nav-dropdown-menu {
    display: block;
}

.nav-dropdown-item {
    padding: 10px;
    border-bottom: 1px solid #eee;
}

.nav-submenu {
    position: absolute;
    left: 100%;
    top: 0;
    background: #fff;
    min-width: 200px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    display: none;
}

.nav-dropdown-item:hover .nav-submenu {
    display: block;
}

/* Steps/Process Section Uniform Boxes */
.steps-row {
    display: flex;
    flex-wrap: wrap;
    gap: 32px;
    justify-content: center;
    align-items: stretch;
    margin-bottom: 32px;
}
.process-step {
    background: #fff;
    border-radius: 18px;
    box-shadow: 0 4px 24px rgba(0,0,0,0.08);
    padding: 32px 24px;
    min-width: 240px;
    max-width: 300px;
    min-height: 340px;
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1 1 240px;
    transition: box-shadow 0.2s;
}
.process-step:hover {
    box-shadow: 0 8px 32px rgba(50,130,184,0.18);
}
.step-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1 1 auto;
}
.step-icon {
    font-size: 2.4rem;
    color: #3282b8;
    margin-bottom: 16px;
}
.step-number {
    font-size: 2rem;
    font-weight: 700;
    color: #1b998b;
    margin-bottom: 8px;
}
.step-title {
    font-size: 1.15rem;
    font-weight: 700;
    margin-bottom: 10px;
    color: #222;
}
.step-description {
    font-size: 1rem;
    color: #444;
}
@media (max-width: 991px) {
    .steps-row {
        flex-direction: column;
        align-items: stretch;
        gap: 20px;
    }
    .process-step {
        max-width: 100%;
        min-width: 0;
    }
}

/* Search Overlay */
.search-overlay {
    position: fixed;
    top: 0;
        left: 0;
        right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.8);
    z-index: 1000;
    display: none;
}

.search-overlay.active {
    display: block;
}

.search-overlay-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #fff;
    padding: 30px;
    border-radius: 5px;
    width: 90%;
    max-width: 600px;
}

.search-input-container {
    position: relative;
    margin-bottom: 20px;
}

.search-input {
    width: 100%;
    padding: 15px 40px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 16px;
}

.search-icon {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
}

.search-overlay-btn {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    background: #007bff;
    color: white;
    border: none;
    padding: 8px 20px;
    border-radius: 20px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.search-overlay-btn:hover {
    background: #0056b3;
}

/* Logo Fixes */
.logo {
    display: flex;
    align-items: center;
    min-width: 160px;
    margin-right: 25px;
}

.logo a {
    display: flex;
    align-items: center;
    pointer-events: none;
}

.logo-img {
    display: inline-block;
    height: auto;
    object-fit: contain;
    max-width: 100%;
}

.logo-img:first-child {
    width: 50px;
    height: 50px;
    margin-right: 5px;
}

.logo-img:last-child {
    width: 70px;
    height: 70px;
}

/* Hero Section */
.hero-section {
    position: relative;
    width: 100%;
    min-height: 100vh;
    background: linear-gradient(rgba(0,0,0,0.55), rgba(0,0,0,0.55)), url('../../img/3.jpg') center/cover no-repeat;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 60px 0 40px 0;
    z-index: 1;
}

.hero-content {
    color: #fff;
    text-shadow: 0 2px 30px rgba(0,0,0,0.3);
    padding: 40px 30px;
}

.hero-title {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 800;
    margin-bottom: 1rem;
    line-height: 1.1;
    letter-spacing: 1px;
    color: #fff;
}

.hero-text {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    color: #e6e6e6;
}

a.btn-explore, button.btn-explore {
    background: linear-gradient(90deg, #3282b8 0%, #1b998b 0%, #3282b8 100%) !important;
    color: #fff !important;
}

a.btn-explore:hover, a.btn-explore:focus,
button.btn-explore:hover, button.btn-explore:focus {
    background: linear-gradient(90deg, #1b998b 0%, #38d39f 100%) !important;
    color: #fff !important;
}

/* Fade-in animation */
@keyframes fadeInUp {
    0% {
        opacity: 0;
        transform: translateY(40px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}
.fade-in-up {
    opacity: 0;
    animation: fadeInUp 1s ease forwards;
}
.delay-200 {
    animation-delay: 0.2s !important;
}
.delay-400 {
    animation-delay: 0.4s !important;
}
.delay-600 {
    animation-delay: 0.6s !important;
}

@media (max-width: 991px) {
    .hero-title {
        font-size: 2.2rem;
    }
    .hero-content {
        padding: 20px 15px;
    }
}
@media (max-width: 767px) {
    .hero-section {
        min-height: 60vh;
        padding: 30px 0 20px 0;
    }
    .hero-title {
        font-size: 1.4rem;
    }
    .hero-text {
        font-size: 1rem;
    }
}

/* Destination Cards */
.destination-cards {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--space-md);
    position: relative;
}

.destination-cards::before {
    content: '';
    position: absolute;
    top: -50px;
    left: -50px;
    width: 150px;
    height: 150px;
    background: radial-gradient(circle, rgba(255,255,255,0.3) 10%, transparent 10%);
    background-size: 15px 15px;
    z-index: 1;
}

.destination-card {
    position: relative;
    height: 350px;
    border-radius: var(--radius-medium);
    overflow: hidden;
    cursor: pointer;
    box-shadow: var(--shadow-medium);
    transition: transform 0.3s ease;
}

.destination-card:hover {
    transform: translateY(-5px);
}

.destination-card img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.destination-card:hover img {
    transform: scale(1.05);
}

.card-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to top, rgba(0,0,0,0.6), rgba(0,0,0,0.1));
}

.destination-card h3 {
    position: absolute;
    bottom: var(--space-md);
    left: var(--space-md);
    color: var(--light-color);
    font-size: var(--fs-600);
    font-weight: 600;
    text-transform: uppercase;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    z-index: 2;
    transition: transform 0.3s ease;
}

.destination-card:hover h3 {
    transform: translateY(-10px);
}

/* Card Slider */
.card-slider-container {
    position: relative;
    width: 100%;
    padding-bottom: 60px;
}

.card-slider {
    width: 100%;
    overflow: visible;
}

.card-slider .swiper-slide {
    height: auto;
    background: none;
}

.card-slider .swiper-slide::before {
    display: none;
}

.card-navigation {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    z-index: 10;
    gap: var(--space-xs);
}

.card-button-prev,
.card-button-next {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border: 1px solid rgba(255, 255, 255, 0.5);
    color: var(--light-color);
    transition: background-color 0.3s ease;
}

.card-button-prev i,
.card-button-next i {
    color: var(--light-color);
    font-size: var(--fs-300);
}

.card-button-prev:hover,
.card-button-next:hover,
.card-button-prev:focus,
.card-button-next:focus {
    background: rgba(255, 255, 255, 0.5);
}

/* Bottom Navigation */
.bottom-navigation {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    z-index: 20;
    gap: var(--space-xs);
}

.nav-button-prev,
.nav-button-next {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.25);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border: 1px solid rgba(255, 255, 255, 0.4);
    color: var(--light-color);
    transition: var(--transition-normal);
}

.nav-button-prev i,
.nav-button-next i {
    font-size: var(--fs-300);
    color: var(--light-color);
}

.nav-button-prev:hover,
.nav-button-next:hover,
.nav-button-prev:focus,
.nav-button-next:focus {
    background: rgba(255, 255, 255, 0.4);
}

/* Horizontal Line */
.horizontal-line {
    position: absolute;
    right: -125px;
    top: 50%;
    width: 120px;
    height: 1px;
    background-color: rgba(255, 255, 255, 0.4);
}

.slide-number {
    position: absolute;
    bottom: 30px;
    right: 50px;
    font-size: var(--fs-900);
    font-weight: 700;
    color: var(--light-color);
    opacity: 0.5;
    z-index: 10;
}

/* Animation Classes for JavaScript */
.hidden {
    opacity: 0;
    transform: translateY(20px);
}

.animate-in {
    animation: fadeIn 0.8s forwards;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Hero Section Mobile Improvements */
@media (max-width: 767px) {
    .hero-content {
        padding: var(--space-lg) 0 var(--space-md);
    text-align: center;
    }

    .hero-title {
        font-size: var(--fs-700);
        margin-top: 60px;
    }

    .hero-content {
        padding: 20px 15px;
    }

    .card-slider-container {
        margin-top: var(--space-md);
    }

    .destination-card {
        height: 250px;
    }

    .horizontal-line {
        width: 60px;
        right: -65px;
    }

    .bottom-navigation {
        bottom: 20px;
    }

    .slide-number {
        bottom: 20px;
        right: 20px;
    }

    .btn-explore {
        margin-bottom: var(--space-md);
    }

    .islands-vertical-container {
        padding: 10px;
    }

    .island-vertical-list {
        gap: 15px;
        padding: 5px;
    }
}

/* Fix for small mobile screens */
@media (max-width: 480px) {
    .hero-title {
        font-size: var(--fs-600);
        margin-top: 80px;
    }

    .destination-card h3 {
        font-size: var(--fs-500);
    }

    .card-slider-container {
        padding-bottom: 30px;
    }

    .mobile-menu a {
        font-size: var(--fs-400);
    }

    .hero-section {
        height: auto;
        min-height: 100vh;
    }

    .mobile-social-icons {
        justify-content: center;
    }

    .islands-vertical-container {
        padding: 5px;
    }

    .island-vertical-list {
        gap: 10px;
        padding: 0;
    }

    .island-card-small {
        height: 100px;
    }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .destination-card:hover img {
        transform: none;
    }

    .destination-card:hover h3 {
        transform: none;
    }

    .btn-explore:hover {
        transform: none;
    }
}

/* Island Grid Layout - Improved */
.islands-grid-container {
    margin-top: var(--space-md);
}

.island-card {
    border-radius: var(--radius-medium);
    overflow: hidden;
    box-shadow: var(--shadow-medium);
    height: 100%;
    background-color: rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(5px);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    display: flex;
    flex-direction: column;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.island-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium), 0 15px 25px rgba(0,0,0,0.2);
    border-color: rgba(255, 255, 255, 0.3);
}

.island-card-image {
    position: relative;
    height: 180px;
    overflow: hidden;
}

.island-card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.island-card:hover .island-card-image img {
    transform: scale(1.05);
}

.card-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to top, rgba(0,0,0,0.7), rgba(0,0,0,0.2));
    z-index: 1;
}

.island-card-content {
    padding: var(--space-sm);
    position: relative;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.island-card h3 {
    color: var(--light-color);
    font-size: var(--fs-500);
    font-weight: 600;
    margin-bottom: var(--space-sm);
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.island-description {
    color: var(--light-color);
    font-size: var(--fs-300);
    margin-bottom: var(--space-sm);
    opacity: 0.9;
}

.btn-small {
    background: var(--accent-color);
    color: var(--light-color);
    padding: 0.4rem 1rem;
    border-radius: 30px;
    text-decoration: none;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: var(--transition-normal);
    display: inline-block;
    font-size: var(--fs-300);
    border: none;
    cursor: pointer;
    align-self: flex-start;
    margin-top: auto;
}

.btn-small:hover,
.btn-small:focus {
    background: var(--secondary-color);
    transform: translateY(-2px);
    color: var(--light-color);
    text-decoration: none;
}

@media (max-width: 767px) {
    .island-card-image {
        height: 150px;
    }

    .island-card h3 {
        font-size: var(--fs-400);
    }

    .island-description {
        font-size: var(--fs-300);
    }
}

@media (max-width: 480px) {
    .island-card-image {
        height: 120px;
    }
}

/* Island Vertical List */
.islands-vertical-container {
    position: relative;
    width: 100%;
    max-width: 400px;
    margin: 0 auto;
    padding: 20px;
    height: auto;
}

.island-vertical-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
    padding: 10px;
}

.island-card-small {
    position: relative;
    width: 100%;
    height: 120px;
    border-radius: 15px;
    overflow: hidden;
    background: #fff;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;
    margin-bottom: 5px;
}

.island-card-image-small {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.island-card-image-small img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.card-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to right, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.3));
    z-index: 2;
}

.island-card-content-small {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 3;
    color: #fff;
    padding: 15px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.island-card-small h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 5px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.hover-info {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.75);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 20px;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 4;
}

.hover-info p {
    color: #fff;
    text-align: center;
    font-size: 0.9rem;
    margin-bottom: 10px;
    transform: translateY(10px);
    opacity: 0;
    transition: all 0.3s ease 0.1s;
}

.view-btn {
    display: inline-block;
    padding: 8px 20px;
    background: var(--accent-color, #007bff);
    color: #fff;
    border-radius: 20px;
    font-weight: 500;
    text-decoration: none;
    transform: translateY(10px);
    opacity: 0;
    transition: all 0.3s ease 0.2s;
}

.view-btn:hover {
    background: var(--secondary-color, #0056b3);
    transform: translateY(-2px);
}

/* Hover Effects */
.island-card-small:hover .hover-info {
    opacity: 1;
}

.island-card-small:hover .hover-info p,
.island-card-small:hover .hover-info .view-btn {
    transform: translateY(0);
    opacity: 1;
}

.island-card-small:hover .island-card-image-small img {
    transform: scale(1.1);
}

.summary-value {
    color: #ffffff;
    font-weight: 500;
}

.btn-proceed {
    background: #00a8b5;
    color: #ffffff;
    padding: 12px 25px;
    border-radius: 4px;
    border: none;
    font-weight: 500;
    margin-top: 20px;
    transition: all 0.3s ease;
}

.btn-proceed:hover {
    background: #008a94;
    transform: translateY(-2px);
}

.nav-about {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #fff;
    text-decoration: none;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.nav-about:hover {
    background: rgba(255, 255, 255, 0.1);
}

.nav-about i {
    font-size: 1.2rem;
}

.nav-about span {
    font-size: 1rem;
}

/* Navigation Links */
.nav-gallery,
.nav-boat,
.nav-islands,
.nav-beaches {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #fff;
    text-decoration: none;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
}

.nav-gallery:hover,
.nav-boat:hover,
.nav-islands:hover,
.nav-beaches:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.nav-gallery i,
.nav-boat i,
.nav-islands i,
.nav-beaches i {
    font-size: 1.1rem;
}

.nav-gallery span,
.nav-boat span,
.nav-islands span,
.nav-beaches span {
    font-size: 0.9rem;
}

/* Transparent Navbar */
.site-header.transparent .nav-gallery,
.site-header.transparent .nav-boat,
.site-header.transparent .nav-islands,
.site-header.transparent .nav-beaches {
    color: #fff;
}

/* White Navbar on Scroll */
.site-header.scrolled .nav-gallery,
.site-header.scrolled .nav-boat,
.site-header.scrolled .nav-islands,
.site-header.scrolled .nav-beaches {
    color: #333;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-gallery,
    .nav-boat,
    .nav-islands,
    .nav-beaches {
        padding: 0.5rem;
    }

    .nav-gallery span,
    .nav-boat span,
    .nav-islands span,
    .nav-beaches span {
        display: none;
    }
}

/* Secondary Navigation Buttons */
.nav-contact,
.nav-book,
.nav-login {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
    text-decoration: none;
}

.nav-contact {
    background-color: #6c757d;
    color: #fff;
}

.nav-book {
    background-color: #28a745;
    color: #fff;
}

.nav-login {
    background-color: #3282b8;
    color: #fff;
}

.nav-contact:hover,
.nav-book:hover,
.nav-login:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    color: #fff;
}

.nav-contact:hover {
    background-color: #5a6268;
}

.nav-book:hover {
    background-color: #218838;
}

.nav-login:hover {
    background-color: #2a6b9c;
}

.nav-contact i,
.nav-book i,
.nav-login i {
    font-size: 1.1rem;
}

/* Social Media Icons */
.nav-social {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-left: 1rem;
}

.nav-social a {
    color: #fff;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.1);
}

.nav-social a:hover {
    transform: translateY(-2px);
    background-color: rgba(255, 255, 255, 0.2);
}

.nav-social .facebook:hover {
    color: #1877f2;
}

.nav-social .instagram:hover {
    color: #e4405f;
}

/* Search Icon */
.nav-search {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.1);
    color: #fff;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    cursor: pointer;
    border: none;
}

.nav-search:hover {
    background-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

/* Scrolled State */
.site-header.scrolled .nav-social a {
    color: #333;
    background-color: rgba(0, 0, 0, 0.05);
}

.site-header.scrolled .nav-search {
    color: #333;
    background-color: rgba(0, 0, 0, 0.05);
}

/* Responsive Design */
@media (max-width: 991px) {
    .nav-contact span,
    .nav-book span,
    .nav-login span {
        display: none;
    }

    .nav-contact,
    .nav-book,
    .nav-login {
        padding: 0.5rem;
    }

    .nav-social {
        margin-left: 0.5rem;
        gap: 0.5rem;
    }

    .nav-social a {
        width: 1.8rem;
        height: 1.8rem;
        font-size: 1rem;
    }
}

@media (max-width: 768px) {
    .nav-contact,
    .nav-book,
    .nav-login {
        padding: 0.4rem;
    }

    .nav-social {
        display: none;
    }
}

/* Boat Navigation */
.nav-boat {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #fff;
    text-decoration: none;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
}

.nav-boat:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.nav-boat i {
    font-size: 1.1rem;
}

.nav-boat span {
    font-size: 0.9rem;
}

/* Transparent Navbar */
.site-header.transparent .nav-boat {
    color: #fff;
}

/* White Navbar on Scroll */
.site-header.scrolled .nav-boat {
    color: #333;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-boat {
        padding: 0.5rem;
    }

    .nav-boat span {
        display: none;
    }
}

/* Responsive Adjustments */
@media (max-width: 1200px) {
    .nav-container {
        padding: 0 1rem;
    }

    .nav-brand a span {
        display: none;
    }

    .secondary-nav a span {
        display: none;
    }
}

@media (max-width: 768px) {
    .nav-container {
        padding: 0 0.5rem;
    }

    .nav-social {
        display: none;
    }

    .secondary-nav {
        gap: 0.25rem;
    }

    .nav-contact, .nav-book, .nav-login {
        padding: 0.5rem;
    }
}

/* Scrolled State */
.site-header.scrolled .nav-brand a {
    color: var(--color-text);
}

.site-header.scrolled .nav-actions a,
.site-header.scrolled .nav-search {
    color: var(--color-text);
}

/* How We Work Section Styles */
.how-we-work-section {
    background: linear-gradient(135deg, var(--light-color) 0%, #f8f9fa 100%);
    padding: var(--space-xl) 0;
    position: relative;
    overflow: hidden;
}

.how-we-work-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('../../img/pattern.png') repeat;
    opacity: 0.05;
    z-index: 0;
}

.how-we-work-section .container {
    position: relative;
    z-index: 1;
}

.process-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: linear-gradient(135deg, var(--accent-color), var(--secondary-color));
    color: var(--light-color);
    border-radius: 50px;
    font-size: var(--fs-300);
    font-weight: 600;
    margin-bottom: var(--space-sm);
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: 0 4px 15px rgba(27, 153, 139, 0.2);
    position: absolute;
    top: -30px;
    left: 50%;
    transform: translateX(-50%);
}

.process-badge i {
    font-size: 1.2rem;
}

.how-we-work-section .section-title {
    color: var(--primary-color);
    font-size: var(--fs-700);
    font-weight: 700;
    margin-top: var(--space-xl);
    margin-bottom: var(--space-sm);
    position: relative;
    display: inline-block;
}

.how-we-work-section .section-subtitle {
    color: var(--text-color);
    font-size: var(--fs-400);
    max-width: 600px;
    margin: 0 auto;
    opacity: 0.8;
}

.process-timeline {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-top: var(--space-xl);
    position: relative;
    padding: 0 var(--space-md);
}

.process-step {
    flex: 1;
    position: relative;
    padding: 0 var(--space-sm);
    display: flex;
    flex-direction: column;
    align-items: center;
}

.step-content {
    background: var(--light-color);
    border-radius: var(--radius-medium);
    padding: var(--space-lg);
    box-shadow: var(--shadow-light);
    transition: var(--transition-normal);
    position: relative;
    z-index: 2;
    width: 100%;
    min-height: 280px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
}

.step-content:hover {
    transform: var(--hover-lift);
    box-shadow: var(--shadow-medium);
}

.step-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, var(--accent-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--space-md);
    color: var(--light-color);
    font-size: 1.8rem;
    transition: var(--transition-normal);
    box-shadow: 0 4px 15px rgba(27, 153, 139, 0.2);
}

.step-content:hover .step-icon {
    transform: scale(1.1) rotate(5deg);
}

.step-info {
    text-align: center;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-sm);
}

.step-number {
    position: absolute;
    top: -15px;
    right: -15px;
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    color: var(--light-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: var(--fs-300);
    box-shadow: 0 4px 10px rgba(15, 52, 96, 0.2);
}

.step-title {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: var(--space-sm);
    font-size: var(--fs-500);
    text-align: center;
}

.step-description {
    color: var(--text-color);
    font-size: var(--fs-300);
    opacity: 0.8;
    line-height: 1.6;
    text-align: center;
    max-width: 250px;
    margin: 0 auto;
}

.step-connector {
    position: absolute;
    top: 50%;
    right: 0;
    width: 100px;
    transform: translateY(-50%);
    display: flex;
    align-items: center;
    z-index: 1;
}

.connector-line {
    flex: 1;
    height: 2px;
    background: linear-gradient(90deg, var(--accent-color), var(--secondary-color));
    opacity: 0.3;
}

.connector-arrow {
    width: 30px;
    height: 30px;
    background: var(--light-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--accent-color);
    font-size: 1rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.process-step:last-child .step-connector {
    display: none;
}

@media (max-width: 991px) {
    .process-timeline {
        flex-direction: column;
        gap: var(--space-lg);
        padding: 0;
    }

    .process-step {
        width: 100%;
        padding: 0;
    }

    .step-connector {
        display: none;
    }

    .step-content {
        max-width: 500px;
        margin: 0 auto;
    }
}

@media (max-width: 576px) {
    .how-we-work-section {
        padding: var(--space-lg) 0;
    }

    .process-badge {
        padding: 0.5rem 1rem;
        font-size: var(--fs-300);
    }

    .step-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .step-number {
        width: 35px;
        height: 35px;
        font-size: var(--fs-300);
    }
}

/* Policies Section Styles */
.policies-section {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    padding: var(--space-xl) 0;
    position: relative;
    overflow: hidden;
}

.policies-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('../../img/pattern.png') repeat;
    opacity: 0.1;
    z-index: 0;
}

.policies-section .container {
    position: relative;
    z-index: 1;
}

.policies-section .section-title {
    text-align: center;
    margin-bottom: var(--space-xl);
}

.policies-section .section-title h2 {
    color: var(--light-color);
    font-size: var(--fs-700);
    font-weight: 700;
    margin-bottom: var(--space-sm);
}

.policies-section .section-title p {
    color: var(--light-color);
    font-size: var(--fs-400);
    opacity: 0.8;
}

.policies-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--space-md);
}

.policy-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-medium);
    padding: var(--space-lg);
    box-shadow: var(--shadow-light);
    transition: var(--transition-normal);
    height: 100%;
    display: flex;
    flex-direction: column;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.policy-card:hover {
    transform: var(--hover-lift);
    box-shadow: var(--shadow-medium);
    background: rgba(255, 255, 255, 0.08);
}

.policy-card h3 {
    color: var(--light-color);
    font-size: var(--fs-500);
    font-weight: 600;
    margin-bottom: var(--space-md);
    padding-bottom: var(--space-sm);
    border-bottom: 2px solid var(--accent-color);
}

.policy-card ul {
    list-style: none;
    padding: 0;
    margin: 0;
    flex: 1;
}

.policy-card li {
    color: var(--light-color);
    font-size: var(--fs-300);
    margin-bottom: var(--space-sm);
    padding-left: var(--space-md);
    position: relative;
    line-height: 1.5;
    opacity: 0.9;
}

.policy-card li::before {
    content: '•';
    color: var(--accent-color);
    position: absolute;
    left: 0;
    font-weight: bold;
}

@media (max-width: 991px) {
    .policies-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 576px) {
    .policies-grid {
        grid-template-columns: 1fr;
    }

    .policies-section {
        padding: var(--space-lg) 0;
    }
}

/* Boats Section Styles - Moved to boats.css */

/* Boat Modal Styles - Moved to boat-modal.css */

/* Boat Modal Media Queries - Moved to boat-modal.css */

/* About Section Styles - Moved to about.css */

/* Simple Gallery Section Styles - Moved to gallery.css */

/* Islands Section Styles - Moved to islands.css */

/* Beaches Section Styles - Moved to beaches.css */

/* Footer Styles - Moved to footer.css */

/* Contact Modal Styles - Moved to contact-modal.css */

.boat-rentals-section {
    padding: var(--space-xl) 0;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    position: relative;
    overflow: hidden;
}

.boat-rentals-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('../img/wave-pattern.png') repeat;
    opacity: 0.1;
    animation: waveAnimation 20s linear infinite;
}

.boat-rentals-section::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100px;
    background: linear-gradient(to top, rgba(255, 255, 255, 0.1), transparent);
}

.boat-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: var(--radius-medium);
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    transition: var(--transition-normal);
    height: 100%;
    border: 1px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(5px);
}

.boat-card:hover {
    transform: translateY(-10px) rotate(1deg);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
    border-color: #4a90e2;
    }

    .boat-image {
    position: relative;
        height: 200px;
    overflow: hidden;
}

.boat-image::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 50%;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.4), transparent);
}

.boat-tag {
    position: absolute;
    top: 15px;
    right: 15px;
    background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
    color: white;
    font-size: var(--fs-300);
    padding: 5px 15px;
    border-radius: 20px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: 0 2px 10px rgba(74, 144, 226, 0.3);
}

.boat-content {
    padding: var(--space-md);
    background: white;
}

.boat-content h3 {
    color: #1e3c72;
    font-size: var(--fs-500);
    margin-bottom: var(--space-sm);
    font-weight: 600;
}

.boat-details p {
    color: #4a5568;
    margin-bottom: 8px;
    font-size: var(--fs-300);
    display: flex;
    align-items: center;
}

.boat-details p i {
    color: #4a90e2;
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

.amenity-tag {
    background: rgba(74, 144, 226, 0.1);
    border: 1px solid rgba(74, 144, 226, 0.2);
    color: #4a90e2;
    font-size: var(--fs-300);
    padding: 4px 10px;
    border-radius: 15px;
    display: inline-block;
    transition: all 0.3s ease;
}

.amenity-tag:hover {
    background: #4a90e2;
    color: white;
    transform: translateY(-3px);
}

.btn-boat-inquiry {
    background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
    color: white;
    padding: 10px 20px;
    border-radius: 30px;
    text-decoration: none;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: var(--transition-normal);
    display: block;
    text-align: center;
    border: none;
    font-size: var(--fs-300);
    box-shadow: 0 4px 15px rgba(74, 144, 226, 0.3);
}

.btn-boat-inquiry:hover {
    background: linear-gradient(135deg, #357abd 0%, #4a90e2 100%);
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(74, 144, 226, 0.4);
}

@keyframes waveAnimation {
    0% {
        background-position: 0 0;
    }
    100% {
        background-position: 100% 0;
    }
}

/* Help Section */
.help-section i {
    color: var(--accent-color);
}

/* Beaches Section Styles */
.beaches-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #0e2a47 0%, #1d4e89 100%);
    position: relative;
    overflow: hidden;
}
.beaches-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0.07;
    pointer-events: none;
}
.beaches-section .section-header {
    text-align: center;
    margin-bottom: 40px;
    position: relative;
    z-index: 2;
}
.beaches-section .section-title {
    color: #fff;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 15px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    letter-spacing: 1px;
}
.beaches-section .section-title::after {
    content: '';
    display: block;
    margin: 18px auto 0 auto;
    width: 80px;
    height: 3px;
    background: linear-gradient(90deg, #3cff00, #1261d0);
    border-radius: 2px;
}
.beaches-section .section-subtitle {
    color: #e2e8f0;
    font-size: 1.1rem;
    max-width: 600px;
    margin: 0 auto;
    opacity: 0.95;
}
.beaches-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 40px;
    position: relative;
    z-index: 2;
}
.beach-card {
    background: rgba(255, 255, 255, 0.07);
    border-radius: 15px;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.13);
    display: flex;
    flex-direction: column;
}
.beach-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    border-color: #3cff00;
}
.beach-image {
    position: relative;
    height: 220px;
    overflow: hidden;
}
.beach-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}
.beach-card:hover .beach-image img {
    transform: scale(1.08);
}
.beach-badge {
    position: absolute;
    top: 18px;
    right: 18px;
    background: linear-gradient(45deg, #3cff00, #1261d0);
    color: #fff;
    padding: 7px 14px;
    border-radius: 18px;
    font-size: 0.85rem;
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(0,0,0,0.13);
    text-transform: uppercase;
    letter-spacing: 1px;
    z-index: 2;
}
.beach-content {
    padding: 22px;
    flex: 1 1 auto;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}
.beach-content h3 {
    color: #fff;
    font-size: 1.4rem;
    margin-bottom: 12px;
    font-weight: 600;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.2);
}
.beach-description {
    color: #cce6ff;
    font-size: 0.99rem;
    line-height: 1.6;
    margin-bottom: 18px;
    opacity: 0.93;
}
.btn-beach-explore {
    display: inline-block;
    background: linear-gradient(45deg, #3cff00, #1261d0);
    color: #fff;
    padding: 10px 25px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    margin-top: auto;
}
.btn-beach-explore:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(60, 255, 0, 0.15);
    color: #fff;
}
@media (max-width: 991px) {
    .beaches-section {
        padding: 60px 0;
    }
    .beaches-grid {
        grid-template-columns: 1fr 1fr;
        gap: 20px;
    }
    .beach-image {
        height: 150px;
    }
}
@media (max-width: 767px) {
    .beaches-section .section-title {
        font-size: 2rem;
    }
    .beaches-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    .beach-image {
        height: 120px;
    }
    .beach-content h3 {
        font-size: 1.1rem;
    }
    .beach-description {
        font-size: 0.91rem;
    }
}
@media (max-width: 480px) {
    .beach-image {
        height: 90px;
    }
}


/* Navbar Styles */
.site-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: transparent;
    transition: all 0.3s ease;
}

.main-nav {
    padding: 1rem 1.5rem;
    transition: all 0.3s ease;
}

/* Transparent state (default/landing) */
.site-header.transparent .main-nav {
    background: transparent;
    backdrop-filter: none;
    -webkit-backdrop-filter: none;
}

.site-header.transparent .nav-link {
    color: white !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.site-header.transparent .nav-link:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white !important;
}

.site-header.transparent .nav-link.active {
    background: rgba(255, 255, 255, 0.2);
    color: white !important;
}

/* Scrolled state */
.site-header.scrolled .main-nav {
    background: #fff;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.site-header.scrolled .nav-link {
    color: #333 !important;
    text-shadow: none;
}

.site-header.scrolled .nav-link:hover {
    background: rgba(0, 0, 0, 0.05);
    color: #000 !important;
}

.site-header.scrolled .nav-link.active {
    background: rgba(0, 0, 0, 0.05);
    color: #000 !important;
}

.nav-container {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0.5rem 1rem;
    gap: 2rem;
}

.nav-logos {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-shrink: 0;
}

.nav-logos img {
    height: 40px;
    width: auto;
}

.nav-brand .nav-logo img {
    height: 40px;
    width: auto;
}

.nav-menu .nav-links {
    display: flex;
    gap: 2rem;
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-link {
    color: white;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    transition: all 0.3s ease;
    white-space: nowrap;
    font-weight: 500;
}

.nav-link i {
    font-size: 1rem;
    margin-right: 3px;
}

.nav-link:hover {
    transform: translateY(-2px);
}

.nav-link.active {
    background: rgba(255, 255, 255, 0.2);
    font-weight: 600;
}

/* Book Now button - Green */
.nav-link.book-now {
    background: #28a745 !important;
    border: 1px solid #28a745;
    color: white !important;
    font-weight: 600;
    margin-left: 1rem;
}

.nav-link.book-now:hover {
    background: #218838 !important;
    border-color: #1e7e34;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
}

.book-now:hover {
    background: rgba(40, 167, 69, 1) !important;
    transform: translateY(-2px);
}

/* Login button - Dark Blue */
.login-btn {
    background: rgba(0, 52, 102, 0.9) !important;
    border: 1px solid rgba(0, 52, 102, 0.4);
    color: white !important;
}

.login-btn:hover {
    background: rgba(0, 52, 102, 1) !important;
    transform: translateY(-2px);
}

/* Keep buttons colored even when scrolled */
.site-header.scrolled .book-now,
.site-header.scrolled .login-btn {
    color: white !important;
}

.site-header.scrolled .book-now:hover,
.site-header.scrolled .login-btn:hover {
    color: white !important;
}

.nav-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.search-toggle, .mobile-menu-toggle {
    background: none;
    border: none;
    color: #fff;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.5rem;
}

.mobile-menu-toggle {
    display: none;
}

/* Mobile Menu Icon */
.menu-icon {
    display: block;
    width: 25px;
    height: 2px;
    background-color: currentColor;
    position: relative;
    transition: all 0.3s ease;
}

.menu-icon::before,
.menu-icon::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background-color: currentColor;
    transition: all 0.3s ease;
}

.menu-icon::before {
    top: -8px;
}

.menu-icon::after {
    bottom: -8px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .mobile-menu-toggle {
        display: block;
    }

    .nav-container {
        padding: 0 1rem;
    }
}

/* Right Navigation Section */
.nav-right {
    display: flex;
    align-items: center;
    margin-left: auto;
    gap: 1rem;
}

.nav-social-icons {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.social-icon {
    border-radius: 50%;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    transition: all 0.3s ease;
    text-decoration: none;
    position: relative;
    cursor: pointer;
    border: none;
    outline: none;
    background-color: transparent;
    pointer-events: auto !important;
}

.social-icon i {
    position: relative;
    font-size: 16px;
    line-height: 1;
    transition: all 0.3s ease;
    pointer-events: none; /* Make sure clicks go through to the parent element */
}

/* Facebook Icon */
.social-icon.facebook {
    background: #1877f2;
    color: white !important;
}

.social-icon.facebook:hover,
.social-icon.facebook.active {
    background: #0d6efd;
    transform: translateY(-2px);
    border-radius: 8px;
}

/* Instagram Icon */
.social-icon.instagram {
    background: #e4405f;
    background: radial-gradient(circle at 30% 107%, #fdf497 0%, #fdf497 5%, #fd5949 45%, #d6249f 60%, #285AEB 90%);
    color: white !important;
}

.social-icon.instagram:hover,
.social-icon.instagram.active {
    opacity: 0.9;
    transform: translateY(-2px);
    border-radius: 8px;
}

/* Search Icon */
.social-icon.search-toggle {
    background: rgba(0, 0, 0, 0.2);
    color: white !important;
}

.social-icon.search-toggle:hover,
.social-icon.search-toggle.active {
    background: rgba(0, 0, 0, 0.3);
    transform: translateY(-2px);
    border-radius: 8px;
}

/* Remove focus outline and black box on click */
.social-icon:focus,
.social-icon:active {
    outline: none;
    box-shadow: none;
    -webkit-tap-highlight-color: transparent;
}

.nav-logos {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.nav-logos img {
    height: 45px;
    width: auto;
    transition: all 0.3s ease;
}

.nav-logos img:hover {
    transform: scale(1.05);
}

@media (max-width: 768px) {
    .nav-social-icons {
        margin-left: 0.5rem;
    }

    .social-icon {
        width: 30px;
        height: 30px;
    }

    .nav-logos img {
        height: 40px;
    }
}

.nav-dropdown {
    position: relative;
}

.nav-dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 8px;
    padding: 0.5rem;
    min-width: 220px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: all 0.3s ease;
    z-index: 1000;
}

.nav-dropdown:hover .nav-dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.nav-dropdown-item {
    color: #333;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    border-radius: 4px;
    transition: all 0.3s ease;
    font-weight: 500;
}

.nav-dropdown-item:hover {
    background: rgba(0, 0, 0, 0.05);
    transform: translateX(5px);
}

.nav-dropdown-item i {
    width: 20px;
    text-align: center;
    font-size: 0.9rem;
}

@media (max-width: 768px) {
    .nav-dropdown-menu {
        position: static;
        background: transparent;
        box-shadow: none;
        padding: 0;
        margin-left: 1rem;
    }

    .nav-dropdown-item {
        padding: 0.5rem 0;
    }
}

/* --- Boat Details Modal Styles --- */
.boat-details-modal {
    display: none;
    position: fixed;
    top: 0; left: 0; width: 100vw; height: 100vh;
    background: rgba(0,0,0,0.45);
    z-index: 2000;
    overflow-y: auto;
    transition: background 0.3s;
}

.boat-details-modal .modal-content {
    background: #fff;
    border-radius: 18px;
    max-width: 700px;
    margin: 60px auto;
    box-shadow: 0 8px 32px rgba(0,0,0,0.18);
    overflow: hidden;
    position: relative;
    padding: 0;
    animation: modalPop 0.25s;
}

@keyframes modalPop {
    from { transform: translateY(40px) scale(0.97); opacity: 0; }
    to { transform: translateY(0) scale(1); opacity: 1; }
}

.boat-details-modal .modal-close-btn {
    position: absolute;
    top: 18px; right: 18px;
    background: #f5f5f5;
    border: none;
    width: 38px; height: 38px;
    border-radius: 50%;
    font-size: 22px;
    color: #333;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0,0,0,0.06);
    transition: background 0.2s, color 0.2s;
}
.boat-details-modal .modal-close-btn:hover {
    background: #e0e0e0;
    color: #d32f2f;
}

.boat-details-modal .modal-body {
    display: flex;
    flex-wrap: wrap;
    gap: 0;
    padding: 0;
}

.boat-details-modal .boat-image {
    flex: 1 1 260px;
    min-width: 260px;
    max-width: 320px;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 32px 0 32px 32px;
}
.boat-details-modal .boat-image img {
    width: 100%;
    max-width: 240px;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.07);
    object-fit: cover;
}

.boat-details-modal .boat-info {
    flex: 2 1 320px;
    padding: 32px 32px 32px 24px;
    background: #fff;
}

.boat-details-modal h2#modalBoatTitle {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 8px;
    color: #1a237e;
}
.boat-details-modal p#modalBoatDescription {
    color: #444;
    margin-bottom: 18px;
    font-size: 1.08rem;
}

.boat-details-modal .boat-features h3,
.boat-details-modal .boat-pricing h3,
.boat-details-modal .boat-availability h3 {
    font-size: 1.08rem;
    font-weight: 600;
    margin: 18px 0 8px;
    color: #1976d2;
}

.boat-details-modal ul#modalBoatFeatures {
    padding-left: 18px;
    margin-bottom: 10px;
}
.boat-details-modal ul#modalBoatFeatures li {
    margin-bottom: 6px;
    color: #333;
    font-size: 1rem;
    list-style: disc;
}

.boat-details-modal .boat-pricing p,
.boat-details-modal .boat-availability p {
    margin: 0 0 10px 0;
    color: #388e3c;
    font-weight: 500;
}

.boat-details-modal .btn-book-now {
    display: inline-block;
    background: linear-gradient(90deg, #43e97b 0%, #38f9d7 100%);
    color: #fff;
    padding: 12px 32px;
    border: none;
    border-radius: 24px;
    font-size: 1.1rem;
    font-weight: 600;
    margin-top: 18px;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(67,233,123,0.08);
    transition: background 0.2s, transform 0.2s;
}
.boat-details-modal .btn-book-now:hover {
    background: linear-gradient(90deg, #38f9d7 0%, #43e97b 100%);
    transform: translateY(-2px) scale(1.04);
}

@media (max-width: 900px) {
    .boat-details-modal .modal-content {
        max-width: 98vw;
    }
    .boat-details-modal .modal-body {
        flex-direction: column;
    }
    .boat-details-modal .boat-image,
    .boat-details-modal .boat-info {
        padding: 24px;
        max-width: 100%;
    }
}

/* Gallery List Modal Styles */
.gallery-list-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    z-index: 2000;
    overflow-y: auto;
}

.gallery-list-modal .modal-content {
    position: relative;
    background-color: #fff;
    width: 90%;
    max-width: 1200px;
    margin: 50px auto;
    border-radius: 15px;
    box-shadow: 0 5px 25px rgba(0, 0, 0, 0.2);
}

.gallery-list-modal .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #eee;
}

.gallery-list-modal .modal-header h3 {
    margin: 0;
    font-size: 24px;
    color: #333;
}

.gallery-list-modal .modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    transition: color 0.3s ease;
}

.gallery-list-modal .modal-close:hover {
    color: #333;
}

.gallery-list-modal .modal-body {
    padding: 20px;
}

.gallery-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.gallery-list-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
    transition: transform 0.3s ease;
}

.gallery-list-item:hover {
    transform: translateY(-5px);
}

.gallery-list-item img {
    width: 100px;
    height: 100px;
    object-fit: cover;
    border-radius: 8px;
}

.gallery-list-item .item-info {
    flex: 1;
}

.gallery-list-item h4 {
    margin: 0 0 5px 0;
    color: #333;
}

.gallery-list-item p {
    margin: 0;
    color: #666;
    font-size: 14px;
}

/* Always show VIEW button and info in hover-info for small cards */
.hover-info p,
.hover-info .view-btn {
    transform: translateY(0) !important;
    opacity: 1 !important;
    transition: none !important;
}
@media (max-width: 991px) {
    .hover-info {
        opacity: 1 !important;
    }
}

@media (max-width: 768px) {
    .gallery-list {
        grid-template-columns: 1fr;
    }
}

/* Ensure the close button for the modal is hidden by default */
.island-modal-close {
    display: none;
}

/* Show the close button only when the modal is active */
.island-modal.active .island-modal-close {
    display: block;
}

/* Boat Status Badge */
.boat-status-badge {
    position: absolute;
    top: 18px;
    left: 18px;
    z-index: 2;
    padding: 6px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: capitalize;
    box-shadow: 0 2px 8px rgba(0,0,0,0.10);
    color: #fff;
    letter-spacing: 0.5px;
    display: inline-block;
}
.boat-status-available {
    background: linear-gradient(90deg, #43e97b 0%, #38f9d7 100%);
}
.boat-status-not-available {
    background: linear-gradient(90deg, #e74c3c 0%, #e67e22 100%);
}
.boat-status-maintenance {
    background: linear-gradient(90deg, #f9d423 0%, #ff4e50 100%);
}

/* Swiper Navigation Button Customization */
.swiper-button-next,
.swiper-button-prev {
    top: 50%;
    transform: translateY(-50%);
    width: 40px;
    height: 40px;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    transition: all 0.2s ease;
}

.swiper-button-next:after,
.swiper-button-prev:after {
    font-size: 18px;
}

.swiper-button-next:hover,
.swiper-button-prev:hover,
.swiper-button-next:focus,
.swiper-button-prev:focus {
    background: rgba(0, 0, 0, 0.7);
}

.swiper-button-next {
    right: 10px;
}

.swiper-button-prev {
    left: 10px;
}

/* Special styling for boats section */
.boats-section .swiper-button-next,
.boats-section .swiper-button-prev {
    background: rgba(0, 0, 0, 0.5);
    width: 40px;
    height: 40px;
}

.boats-section .swiper-button-next:hover,
.boats-section .swiper-button-prev:hover {
    background: rgba(0, 0, 0, 0.7);
}

@media (max-width: 900px) {
    .swiper-button-next {
        right: 5px;
    }
    .swiper-button-prev {
        left: 5px;
    }
}

.swiper-pagination {
    bottom: -5px !important;
}

.boats-section .swiper-pagination {
    bottom: -30px !important;
}

.boats-section .swiper-container {
    padding-bottom: 30px;
}

.boats-section .swiper-pagination-bullet {
    width: 10px;
    height: 10px;
    margin: 0 5px;
}

/* ===============================
   BOAT STATUS BADGES
   =============================== */
.boat-status-badge {
    position: absolute;
    top: 18px;
    left: 18px;
    z-index: 2;
    padding: 6px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: capitalize;
    box-shadow: 0 2px 8px rgba(0,0,0,0.10);
    color: #fff;
    letter-spacing: 0.5px;
    display: inline-block;
}
.boat-status-available {
    background: linear-gradient(90deg, #43e97b 0%, #38f9d7 100%);
}
.boat-status-not-available {
    background: linear-gradient(90deg, #e74c3c 0%, #e67e22 100%);
}
.boat-status-maintenance {
    background: linear-gradient(90deg, #f9d423 0%, #ff4e50 100%);
}

/* ===============================
   ISLAND MODAL CLOSE BUTTON
   =============================== */
.island-modal.active .island-modal-close {
    display: block;
}

