<?php
class Database {
    private $pdo;
    private static $instance = null;
    private $cache = [];
    private $cacheEnabled;

    private function __construct($pdo) {
        $this->pdo = $pdo;
        $this->cacheEnabled = defined('CACHE_ENABLED') ? CACHE_ENABLED : false;
    }

    public static function getInstance($pdo) {
        if (self::$instance === null) {
            self::$instance = new self($pdo);
        }
        return self::$instance;
    }

    public function query($sql, $params = [], $useCache = true) {
        $cacheKey = $this->generateCacheKey($sql, $params);
        
        // Check cache if enabled
        if ($useCache && $this->cacheEnabled && isset($this->cache[$cacheKey])) {
            $cacheData = $this->cache[$cacheKey];
            if (time() - $cacheData['time'] < CACHE_DURATION) {
                return $cacheData['data'];
            }
        }

        try {
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            
            $result = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Cache the result if enabled
            if ($useCache && $this->cacheEnabled) {
                $this->cache[$cacheKey] = [
                    'time' => time(),
                    'data' => $result
                ];
            }
            
            return $result;
        } catch (PDOException $e) {
            throw new Exception("Database error occurred: " . $e->getMessage());
        }
    }

    public function queryOne($sql, $params = [], $useCache = true) {
        $results = $this->query($sql, $params, $useCache);
        return $results ? $results[0] : null;
    }

    public function count($sql, $params = []) {
        try {
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchColumn();
        } catch (PDOException $e) {
            throw new Exception("Database error occurred");
        }
    }

    public function insert($table, $data) {
        $columns = array_keys($data);
        $values = array_values($data);
        $placeholders = str_repeat('?,', count($columns) - 1) . '?';
        
        $sql = "INSERT INTO {$table} (" . implode(',', $columns) . ") VALUES ({$placeholders})";
        
        try {
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($values);
            return $this->pdo->lastInsertId();
        } catch (PDOException $e) {
            // error_log("Database Error: " . $e->getMessage()); // (Tanggalin sa production kung di na kailangan)
            throw new Exception("Database error occurred");
        }
    }

    public function update($table, $data, $where, $whereParams = []) {
        $set = implode('=?,', array_keys($data)) . '=?';
        $sql = "UPDATE {$table} SET {$set} WHERE {$where}";
        
        try {
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute(array_merge(array_values($data), $whereParams));
            return $stmt->rowCount();
        } catch (PDOException $e) {
            // error_log("Database Error: " . $e->getMessage()); // (Tanggalin sa production kung di na kailangan)
            throw new Exception("Database error occurred");
        }
    }

    public function delete($table, $where, $params = []) {
        $sql = "DELETE FROM {$table} WHERE {$where}";
        
        try {
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            return $stmt->rowCount();
        } catch (PDOException $e) {
            // error_log("Database Error: " . $e->getMessage()); // (Tanggalin sa production kung di na kailangan)
            throw new Exception("Database error occurred");
        }
    }

    public function beginTransaction() {
        return $this->pdo->beginTransaction();
    }

    public function commit() {
        return $this->pdo->commit();
    }

    public function rollBack() {
        return $this->pdo->rollBack();
    }

    private function generateCacheKey($sql, $params) {
        return md5($sql . serialize($params));
    }

    public function clearCache() {
        $this->cache = [];
    }

    // New static method to setup database tables and default admin user
    public static function setupDatabase($pdo) {
        try {
            // Create users table
            $sql = "CREATE TABLE IF NOT EXISTS users (
                user_id INT AUTO_INCREMENT PRIMARY KEY,
                full_name VARCHAR(100) NOT NULL,
                email VARCHAR(100) NOT NULL,
                password VARCHAR(255) NOT NULL,
                date_of_birth DATE DEFAULT NULL,
                age INT DEFAULT NULL,
                gender ENUM('Male','Female','Other') DEFAULT NULL,
                nationality VARCHAR(50) DEFAULT NULL,
                address TEXT DEFAULT NULL,
                phone VARCHAR(20) DEFAULT NULL,
                user_type ENUM('Customer','Admin') DEFAULT 'Customer',
                is_verified TINYINT(1) DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                UNIQUE KEY email (email)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

            $pdo->exec($sql);

            // Check if default admin user exists
            $stmt = $pdo->prepare("SELECT user_id FROM users WHERE email = ?");
            $adminEmail = '<EMAIL>';
            $stmt->execute([$adminEmail]);
            $exists = $stmt->fetch();

            if (!$exists) {
                $adminPassword = password_hash('password', PASSWORD_DEFAULT);
                $insertSql = "INSERT INTO users (full_name, email, password, user_type, is_verified) 
                              VALUES ('Admin User', ?, ?, 'Admin', 1)";
                $insertStmt = $pdo->prepare($insertSql);
                $insertStmt->execute([$adminEmail, $adminPassword]);
            }

            echo "Database setup completed successfully.";
        } catch (PDOException $e) {
            echo "Database setup error: " . $e->getMessage();
        }
    }
}
?>
