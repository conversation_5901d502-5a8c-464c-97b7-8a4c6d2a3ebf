[2025-05-06 10:51:14] Received booking data - Data: {"selectedBoat":"AssignedByTourismOffice","bookingId":"BOAT-20250506-19075","currentDate":"2025-05-06","loaded":"true","firstName":"<PERSON>","lastName":"<PERSON>","suffix":"","age":"21","sex":"Male","contactNumber":"09569796596","emailAddress":"<EMAIL>","completeAddress":"Iloilo City","emergencyName":"Me","emergencyNumber":"09569565965","locationTourDestination":"Gigantes Island","dropOffLocation":"Estancia","numberOfPax":"25","regularPax":"25","discountedPax":"0","childrenPax":"0","infantsPax":"0","totalEnvironmentalFee":"1875","startDate":"2025-05-09","endDate":"2025-05-15","total":"1875.00","duration":"7 day(s)","status":"verification_pending"}
[2025-05-06 10:58:48] Received booking data - Data: {"selectedBoat":"AssignedByTourismOffice","bookingId":"BOAT-20250506-61192","currentDate":"2025-05-06","loaded":"true","firstName":"Ralph","lastName":"Ramos","suffix":"","age":"21","sex":"Male","contactNumber":"09459464986","emailAddress":"<EMAIL>","completeAddress":"Iloilo","emergencyName":"Me","emergencyNumber":"09495469858","locationTourDestination":"Gigantes Island","dropOffLocation":"Estancia","numberOfPax":"25","regularPax":"25","discountedPax":"0","childrenPax":"0","infantsPax":"0","totalEnvironmentalFee":"1875","startDate":"2025-05-09","endDate":"2025-05-15","paymentMethod":"Manual Payment","total":"1875.00","duration":"6 day(s)","status":"verification_pending"}
[2025-05-06 11:09:12] Received booking data - Data: {"selectedBoat":"AssignedByTourismOffice","bookingId":"BOAT-20250505-49757","currentDate":"2025-05-06","loaded":"true","firstName":"Raphael ","lastName":"Ramos","suffix":"","age":"21","sex":"Male","contactNumber":"09459454954","emailAddress":"<EMAIL>","completeAddress":"Iloilo","emergencyName":"Me","emergencyNumber":"09496946496","locationTourDestination":"Gigantes Island","dropOffLocation":"Estancia","numberOfPax":"25","regularPax":"25","discountedPax":"0","childrenPax":"0","infantsPax":"0","totalEnvironmentalFee":"1875","startDate":"2025-05-08","endDate":"2025-05-15","paymentMethod":"Manual Payment","total":"1875.00","duration":"7 day(s)","status":"verification_pending"}
[2025-05-06 11:09:12] Booking inserted successfully: BOAT-20250506-51738
[2025-05-06 11:09:12] New booking ID: 29
[2025-05-06 11:09:17] Email sent <NAME_EMAIL>
[2025-05-06 11:12:08] Received booking data - Data: {"selectedBoat":"AssignedByTourismOffice","bookingId":"BOAT-20250506-77230","currentDate":"2025-05-06","loaded":"true","firstName":"Lhance","lastName":"Montero","suffix":"","age":"21","sex":"Male","contactNumber":"09662387896","emailAddress":"<EMAIL>","completeAddress":"Estancia","emergencyName":"Me","emergencyNumber":"09494964694","locationTourDestination":"Gigantes Island","dropOffLocation":"Estancia","numberOfPax":"5","regularPax":"2","discountedPax":"2","childrenPax":"1","infantsPax":"0","totalEnvironmentalFee":"330","startDate":"2025-05-08","endDate":"2025-05-16","paymentMethod":"Manual Payment","total":"330.00","duration":"7 day(s)","status":"verification_pending"}
[2025-05-06 11:12:08] Booking inserted successfully: BOAT-20250506-77116
[2025-05-06 11:12:08] New booking ID: 30
[2025-05-06 11:12:13] Email sent <NAME_EMAIL>
[2025-05-06 11:15:09] Received booking data - Data: {"selectedBoat":"AssignedByTourismOffice","bookingId":"BOAT-20250506-36364","currentDate":"2025-05-06","loaded":"true","firstName":"Jhona Mae","lastName":"Santander","suffix":"","age":"21","sex":"Female","contactNumber":"09489584468","emailAddress":"<EMAIL>","completeAddress":"Sicogon Island","emergencyName":"Jhona Mae","emergencyNumber":"09569656589","locationTourDestination":"Gigantes Island","dropOffLocation":"Estancia","numberOfPax":"7","regularPax":"1","discountedPax":"4","childrenPax":"2","infantsPax":"0","totalEnvironmentalFee":"435","startDate":"2025-05-09","endDate":"2025-05-23","paymentMethod":"Manual Payment","total":"435.00","duration":"7 day(s)","status":"verification_pending"}
[2025-05-06 11:15:09] Booking inserted successfully: BOAT-20250506-47212
[2025-05-06 11:15:09] New booking ID: 31
[2025-05-06 11:15:14] Email sent <NAME_EMAIL>
[2025-05-06 11:38:39] Received booking data - Data: {"selectedBoat":"AssignedByTourismOffice","bookingId":"BOAT-20250506-70244","currentDate":"2025-05-06","loaded":"true","firstName":"Jordan ","lastName":"Barcarlos","suffix":"","age":"21","sex":"Male","contactNumber":"09964864548","emailAddress":"<EMAIL>","completeAddress":"Pani-an, Balasan, Iloilo","emergencyName":"Jordan","emergencyNumber":"09459459456","locationTourDestination":"Sicogon Island","dropOffLocation":"Balasan","numberOfPax":"5","regularPax":"2","discountedPax":"2","childrenPax":"1","infantsPax":"0","totalEnvironmentalFee":"330","startDate":"2025-05-09","endDate":"2025-05-15","paymentMethod":"Manual Payment","total":"330.00","duration":"6 day(s)","status":"verification_pending"}
[2025-05-06 11:38:39] Booking inserted successfully: BOAT-20250506-98574
[2025-05-06 11:38:39] New booking ID: 32
[2025-05-06 11:38:42] Email sent <NAME_EMAIL>
[2025-05-06 16:26:44] Received booking data - Data: {"selectedBoat":"AssignedByTourismOffice","bookingId":"BOAT-20250506-20049","currentDate":"2025-05-06","loaded":"true","firstName":"Lhance","lastName":"Montero","suffix":"","age":"21","sex":"Male","contactNumber":"09355544656","emailAddress":"<EMAIL>","completeAddress":"Estancia Iloilo","emergencyName":"Me","emergencyNumber":"0975656556","locationTourDestination":"Gigantes Island","dropOffLocation":"Estancia","numberOfPax":"4","regularPax":"2","discountedPax":"1","childrenPax":"1","infantsPax":"0","totalEnvironmentalFee":"270","startDate":"2025-05-07","endDate":"2025-05-13","paymentMethod":"Manual Payment","total":"270.00","duration":"6 day(s)","status":"verification_pending"}
[2025-05-06 16:26:44] Setting destination name: - Data: {"destination":"Gigantes Island"}
[2025-05-06 16:26:44] SQL Query: - Data: {"query":"INSERT INTO bookings (\n            booking_code, first_name, last_name, suffix, age, sex, email, contact_number,\n            address, drop_off_location, no_of_pax, start_date, end_date, booking_status, created_at,\n            is_today_booking, tour_destination, emergency_name, emergency_number,\n            environmental_fee, payment_method, total\n        ) VALUES (\n            'BOAT-20250506-20049', 'Lhance', 'Montero', '', 21, 'Male',\n            '<EMAIL>', '09355544656', 'Estancia Iloilo', 'Estancia', 4,\n            '2025-05-07', '2025-05-13', 'pending', NOW(),\n            1, 'Gigantes Island', 'Me', '0975656556',\n            270, 'Manual Payment', 270\n        )"}
[2025-05-06 16:31:56] Received booking data - Data: {"selectedBoat":"AssignedByTourismOffice","bookingId":"BOAT-20250506-41084","currentDate":"2025-05-06","loaded":"true","firstName":"Lhance","lastName":"Montero","suffix":"","age":"21","sex":"Male","contactNumber":"09677657575","emailAddress":"<EMAIL>","completeAddress":"Estancia","emergencyName":"Me","emergencyNumber":"09447646454","locationTourDestination":"Gigantes Island","dropOffLocation":"Estancia","numberOfPax":"4","regularPax":"2","discountedPax":"1","childrenPax":"1","infantsPax":"0","totalEnvironmentalFee":"270","startDate":"2025-05-07","endDate":"2025-05-13","paymentMethod":"Manual Payment","total":"270.00","duration":"6 day(s)","status":"verification_pending"}
[2025-05-06 16:31:56] Setting destination name: - Data: {"destination":"Gigantes Island"}
[2025-05-06 16:31:56] SQL Query: - Data: {"query":"INSERT INTO bookings (\n            booking_code, first_name, last_name, suffix, age, sex, email, contact_number,\n            address, drop_off_location, no_of_pax, start_date, end_date, booking_status, created_at,\n            is_today_booking, tour_destination, emergency_name, emergency_number,\n            environmental_fee, payment_method, total\n        ) VALUES (\n            'BOAT-20250506-41084', 'Lhance', 'Montero', '', 21, 'Male',\n            '<EMAIL>', '09677657575', 'Estancia', 'Estancia', 4,\n            '2025-05-07', '2025-05-13', 'pending', NOW(),\n            1, 'Gigantes Island', 'Me', '09447646454',\n            270, 'Manual Payment', 270\n        )"}
[2025-05-06 17:00:19] Received booking data - Data: {"selectedBoat":"AssignedByTourismOffice","bookingId":"BOAT-20250506-48143","currentDate":"2025-05-06","loaded":"true","firstName":"Lhance","lastName":"Montero","suffix":"","age":"21","sex":"Male","contactNumber":"09945954598","emailAddress":"<EMAIL>","completeAddress":"Estancia Iloilo","emergencyName":"Me","emergencyNumber":"09494869456","locationTourDestination":"Gigantes Island","dropOffLocation":"Estancia","numberOfPax":"25","regularPax":"25","discountedPax":"0","childrenPax":"0","infantsPax":"0","totalEnvironmentalFee":"1875","startDate":"2025-05-07","endDate":"2025-05-13","paymentMethod":"Manual Payment","total":"1875.00","duration":"6 day(s)","status":"verification_pending"}
[2025-05-06 17:00:19] Setting destination name: - Data: {"destination":"Gigantes Island"}
[2025-05-06 17:00:19] SQL Query: - Data: {"query":"INSERT INTO bookings (\n            booking_code, first_name, last_name, suffix, age, sex, email, contact_number,\n            address, drop_off_location, no_of_pax, start_date, end_date, booking_status, created_at,\n            is_today_booking, tour_destination, emergency_name, emergency_number,\n            environmental_fee, payment_method, total\n        ) VALUES (\n            'BOAT-20250506-48143', 'Lhance', 'Montero', '', 21, 'Male',\n            '<EMAIL>', '09945954598', 'Estancia Iloilo', 'Estancia', 25,\n            '2025-05-07', '2025-05-13', 'pending', NOW(),\n            1, 'Gigantes Island', 'Me', '09494869456',\n            1875, 'Manual Payment', 1875\n        )"}
[2025-05-06 17:18:18] Received booking data - Data: {"selectedBoat":"AssignedByTourismOffice","bookingId":"BOAT-20250506-18899","currentDate":"2025-05-06","loaded":"true","firstName":"Lhance","lastName":"Montero","suffix":"","age":"1","sex":"Male","contactNumber":"09393335985","emailAddress":"<EMAIL>","completeAddress":"Estancia Iloilo","emergencyName":"Me","emergencyNumber":"095595995","locationTourDestination":"Gigantes Island","dropOffLocation":"Estancia","numberOfPax":"25","regularPax":"25","discountedPax":"0","childrenPax":"0","infantsPax":"0","totalEnvironmentalFee":"1875","startDate":"2025-05-15","endDate":"2025-05-16","paymentMethod":"Manual Payment","total":"1875.00","duration":"6 day(s)","status":"verification_pending"}
[2025-05-06 17:18:18] Setting destination name: - Data: {"destination":"Gigantes Island"}
[2025-05-06 17:18:18] SQL Query: - Data: {"query":"INSERT INTO bookings (\n            booking_code, first_name, last_name, suffix, age, sex, email, contact_number,\n            address, drop_off_location, no_of_pax, start_date, end_date, booking_status, created_at,\n            is_today_booking, tour_destination, emergency_name, emergency_number,\n            environmental_fee, payment_method, total\n        ) VALUES (\n            'BOAT-20250506-18899', 'Lhance', 'Montero', '', 1, 'Male',\n            '<EMAIL>', '09393335985', 'Estancia Iloilo', 'Estancia', 25,\n            '2025-05-15', '2025-05-16', 'pending', NOW(),\n            1, 'Gigantes Island', 'Me', '095595995',\n            1875, 'Manual Payment', 1875\n        )"}
[2025-05-06 17:26:08] Received booking data - Data: {"selectedBoat":"AssignedByTourismOffice","bookingId":"BOAT-20250506-40825","currentDate":"2025-05-06","loaded":"true","firstName":"Lhance","lastName":"Montero","suffix":"","age":"21","sex":"Male","contactNumber":"09494695469","emailAddress":"<EMAIL>","completeAddress":"Estancia","emergencyName":"Me","emergencyNumber":"095595995","locationTourDestination":"Gigantes Island","dropOffLocation":"Estancia","numberOfPax":"3","regularPax":"1","discountedPax":"1","childrenPax":"1","infantsPax":"0","totalEnvironmentalFee":"195","startDate":"2025-05-15","endDate":"2025-05-16","paymentMethod":"Manual Payment","total":"195.00","duration":"1 day(s)","status":"verification_pending"}
[2025-05-06 17:26:08] Setting destination name: - Data: {"destination":"Gigantes Island"}
[2025-05-06 17:26:08] SQL Query: - Data: {"query":"INSERT INTO bookings (\n            booking_code, first_name, last_name, suffix, age, sex, email, contact_number,\n            address, drop_off_location, no_of_pax, start_date, end_date, booking_status, created_at,\n            is_today_booking, tour_destination, emergency_name, emergency_number,\n            environmental_fee, payment_method, total\n        ) VALUES (\n            'BOAT-20250506-40825', 'Lhance', 'Montero', '', 21, 'Male',\n            '<EMAIL>', '09494695469', 'Estancia', 'Estancia', 3,\n            '2025-05-15', '2025-05-16', 'pending', NOW(),\n            1, 'Gigantes Island', 'Me', '095595995',\n            195, 'Manual Payment', 195\n        )"}
[2025-05-06 23:31:20] Received booking data - Data: {"selectedBoat":"AssignedByTourismOffice","bookingId":"BOAT-20250506-28771","currentDate":"2025-05-07","loaded":"true","firstName":"Raphael Ralph","lastName":"Ramos","suffix":"","age":"21","sex":"Male","contactNumber":"09T49496464","emailAddress":"<EMAIL>","completeAddress":"Estancia, Iloillo","emergencyName":"Carlo Ramos","emergencyNumber":"09389583855","locationTourDestination":"Gigantes Island","dropOffLocation":"Estancia","numberOfPax":"4","regularPax":"1","discountedPax":"1","childrenPax":"2","infantsPax":"0","totalEnvironmentalFee":"255","startDate":"2025-05-10","endDate":"2025-05-15","paymentMethod":"Manual Payment","total":"255.00","duration":"5 day(s)","status":"verification_pending"}
[2025-05-06 23:31:20] Setting destination name: - Data: {"destination":"Gigantes Island"}
[2025-05-06 23:31:20] SQL Query: - Data: {"query":"INSERT INTO bookings (\n            booking_code, first_name, last_name, suffix, age, sex, email, contact_number,\n            address, drop_off_location, no_of_pax, start_date, end_date, booking_status, created_at,\n            is_today_booking, tour_destination, emergency_name, emergency_number,\n            environmental_fee, payment_method, total\n        ) VALUES (\n            'BOAT-20250506-28771', 'Raphael Ralph', 'Ramos', '', 21, 'Male',\n            '<EMAIL>', '09T49496464', 'Estancia, Iloillo', 'Estancia', 4,\n            '2025-05-10', '2025-05-15', 'pending', NOW(),\n            1, 'Gigantes Island', 'Carlo Ramos', '09389583855',\n            255, 'Manual Payment', 255\n        )"}
[2025-05-06 23:34:16] Received booking data - Data: {"selectedBoat":"AssignedByTourismOffice","bookingId":"BOAT-20250507-78275","currentDate":"2025-05-07","loaded":"true","firstName":"Raphael Ralph","lastName":"Ramos","suffix":"Jr.","age":"21","sex":"Male","contactNumber":"09662387896","emailAddress":"<EMAIL>","completeAddress":"Estancia,Iloilo","emergencyName":"Me","emergencyNumber":"09494946846","locationTourDestination":"Gigantes Island","dropOffLocation":"Estancia","numberOfPax":"3","regularPax":"2","discountedPax":"1","childrenPax":"0","infantsPax":"0","totalEnvironmentalFee":"210","startDate":"2025-05-10","endDate":"2025-05-15","paymentMethod":"Manual Payment","total":"210.00","duration":"5 day(s)","status":"verification_pending"}
[2025-05-06 23:34:16] Setting destination name: - Data: {"destination":"Gigantes Island"}
[2025-05-06 23:34:16] SQL Query: - Data: {"query":"INSERT INTO bookings (\n            booking_code, first_name, last_name, suffix, age, sex, email, contact_number,\n            address, drop_off_location, no_of_pax, start_date, end_date, booking_status, created_at,\n            is_today_booking, tour_destination, emergency_name, emergency_number,\n            environmental_fee, payment_method, total\n        ) VALUES (\n            'BOAT-20250507-78275', 'Raphael Ralph', 'Ramos', 'Jr.', 21, 'Male',\n            '<EMAIL>', '09662387896', 'Estancia,Iloilo', 'Estancia', 3,\n            '2025-05-10', '2025-05-15', 'pending', NOW(),\n            1, 'Gigantes Island', 'Me', '09494946846',\n            210, 'Manual Payment', 210\n        )"}
[2025-05-07 00:10:18] Received booking data - Data: {"selectedBoat":"AssignedByTourismOffice","bookingId":"BOAT-20250507-15531","currentDate":"2025-05-07","loaded":"true","firstName":"Raphael Ralph","lastName":"Ramos","suffix":"","age":"19","sex":"Male","contactNumber":"09555854845","emailAddress":"<EMAIL>","completeAddress":"Carles","emergencyName":"me","emergencyNumber":"09566757575","locationTourDestination":"Gigantes Island","dropOffLocation":"Estancia","numberOfPax":"25","regularPax":"25","discountedPax":"0","childrenPax":"0","infantsPax":"0","totalEnvironmentalFee":"1875","startDate":"2025-05-09","endDate":"2025-05-09","paymentMethod":"Manual Payment","total":"1875.00","duration":"0 day(s)","status":"verification_pending"}
[2025-05-07 00:10:18] Setting destination name: - Data: {"destination":"Gigantes Island"}
[2025-05-07 00:10:18] SQL Query: - Data: {"query":"INSERT INTO bookings (\n            booking_code, first_name, last_name, suffix, age, sex, email, contact_number,\n            address, drop_off_location, no_of_pax, start_date, end_date, booking_status, created_at,\n            is_today_booking, tour_destination, emergency_name, emergency_number,\n            environmental_fee, payment_method, total\n        ) VALUES (\n            'BOAT-20250507-15531', 'Raphael Ralph', 'Ramos', '', 19, 'Male',\n            '<EMAIL>', '09555854845', 'Carles', 'Estancia', 25,\n            '2025-05-09', '2025-05-09', 'pending', NOW(),\n            1, 'Gigantes Island', 'me', '09566757575',\n            1875, 'Manual Payment', 1875\n        )"}
