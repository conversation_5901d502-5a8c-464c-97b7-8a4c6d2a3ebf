/* About Section Styles */
.about-section {
    padding: 80px 0;
    background: #f8f9fa;
    position: relative;
}

.about-section .section-header {
    text-align: center;
    margin-bottom: 50px;
}

.about-section .section-title {
    color: #333;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 15px;
}

.about-section .section-subtitle {
    color: #666;
    font-size: 1.1rem;
    max-width: 600px;
    margin: 0 auto;
}

.about-content {
    margin-top: 30px;
}

.about-image {
    position: relative;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.about-image img {
    width: 100%;
    height: auto;
    display: block;
}

.about-text {
    padding: 20px;
}

.about-text h3 {
    color: #0f3460;
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 15px;
}

.about-text p {
    color: #555;
    font-size: 1rem;
    line-height: 1.6;
    margin-bottom: 20px;
}

.values-list {
    list-style: none;
    padding: 0;
    margin: 0 0 30px;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
}

.values-list li {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #555;
    font-size: 1rem;
}

.values-list li i {
    color: #0f3460;
    font-size: 1.2rem;
}

.about-stats {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    margin-top: 30px;
}

.stat-item {
    flex: 1 1 150px;
    text-align: center;
}

.stat-number {
    display: block;
    color: #0f3460;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 5px;
}

.stat-label {
    display: block;
    color: #666;
    font-size: 0.9rem;
}

/* Responsive Styles */
@media (max-width: 991px) {
    .about-section {
        padding: 60px 0;
    }
    
    .about-section .section-title {
        font-size: 2rem;
    }
    
    .about-section .section-subtitle {
        font-size: 1rem;
    }
    
    .about-text h3 {
        font-size: 1.5rem;
    }
    
    .values-list {
        grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    }
    
    .stat-number {
        font-size: 2rem;
    }
}

@media (max-width: 767px) {
    .about-section {
        padding: 50px 0;
    }
    
    .about-section .section-title {
        font-size: 1.8rem;
    }
    
    .about-text {
        padding: 15px 0;
    }
    
    .about-text h3 {
        font-size: 1.3rem;
    }
    
    .about-text p {
        font-size: 0.95rem;
    }
    
    .values-list li {
        font-size: 0.95rem;
    }
    
    .values-list li i {
        font-size: 1.1rem;
    }
    
    .stat-number {
        font-size: 1.8rem;
    }
    
    .stat-label {
        font-size: 0.85rem;
    }
}

@media (max-width: 576px) {
    .values-list {
        grid-template-columns: 1fr;
    }
    
    .about-stats {
        flex-direction: column;
        gap: 20px;
    }
}
