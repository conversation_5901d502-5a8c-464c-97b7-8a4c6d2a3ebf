/* Islands Section Styles */
.islands-section {
    padding: 80px 0;
    background: #ffffff;
    position: relative;
    overflow: hidden;
}

.islands-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('../../img/wave-pattern.png') repeat;
    opacity: 0.05;
    z-index: 0;
}

.islands-section .section-header {
    text-align: center;
    margin-bottom: 50px;
    position: relative;
    z-index: 1;
}

.islands-section .section-title {
    color: #333333;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 15px;
    text-shadow: none;
}

.islands-section .section-subtitle {
    color: #666666;
    font-size: 1.1rem;
    max-width: 600px;
    margin: 0 auto;
}

.islands-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 30px;
    position: relative;
    z-index: 1;
}

.island-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.island-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
    border-color: rgba(255, 255, 255, 0.2);
}

.island-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.island-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.island-card:hover .island-image img {
    transform: scale(1.1);
}

.island-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.5));
    display: flex;
    align-items: flex-start;
    justify-content: flex-end;
    padding: 15px;
}

.island-badge {
    background: linear-gradient(135deg, #ff9a44, #fc6076);
    color: white;
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
}

.island-content {
    padding: 20px;
}

.island-content h3 {
    color: #ffffff;
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 10px;
}

.island-description {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.95rem;
    margin-bottom: 15px;
    line-height: 1.5;
}

.island-features {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.feature {
    display: flex;
    align-items: center;
    gap: 5px;
    background: rgba(255, 255, 255, 0.1);
    padding: 5px 10px;
    border-radius: 5px;
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.9);
}

.feature i {
    color: #4CAF50;
}

/* Island Modal Styles */
.island-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 1000;
    justify-content: center;
    align-items: center;
}

.island-modal-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.island-modal-content {
    position: relative;
    background-color: #fff;
    padding: 25px;
    width: 90%;
    max-width: 600px;
    border-radius: 15px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.4);
    z-index: 1001;
    max-height: 80vh;
    overflow-y: auto;
    animation: modalSlideIn 0.3s ease-out;
    margin: 0;
}

.modal-close-btn {
    position: absolute;
    top: 15px;
    right: 15px;
    background: rgba(255, 255, 255, 0.95);
    color: #333;
    border: none;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 1002;
    font-size: 16px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    z-index: 1002;
}

.modal-close-btn:hover {
    background: #f5f5f5;
    transform: rotate(90deg);
}

.island-modal-content h2 {
    color: #0f3460;
    font-size: 2rem;
    font-weight: 700;
    margin-top: 0;
    margin-bottom: 12px;
}

.island-modal-content p {
    color: #555;
    font-size: 1rem;
    line-height: 1.6;
    margin-bottom: 15px;
}

.island-modal-content ul {
    list-style: none;
    padding: 0;
    margin: 0 0 15px;
}

.island-modal-content ul li {
    margin-bottom: 8px;
    color: #555;
    font-size: 0.95rem;
    position: relative;
    padding-left: 25px;
}

.island-modal-content ul li:before {
    content: "✓";
    position: absolute;
    left: 0;
    color: #4CAF50;
    font-weight: bold;
}

.island-modal-image-container {
    margin-top: 15px;
    margin-bottom: 0;
    position: relative;
    overflow: hidden;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    height: 250px;
}

.island-modal-image-container img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.island-modal-image-container:hover img {
    transform: scale(1.05);
}

/* Responsive Styles */
@media (max-width: 991px) {
    .islands-section {
        padding: 60px 0;
    }

    .islands-section .section-title {
        font-size: 2rem;
    }

    .islands-section .section-subtitle {
        font-size: 1rem;
    }

    .islands-grid {
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 20px;
    }

    .island-image {
        height: 180px;
    }
}

@media (max-width: 767px) {
    .islands-section {
        padding: 50px 0;
    }

    .islands-section .section-title {
        font-size: 1.8rem;
    }

    .islands-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 15px;
    }

    .island-content h3 {
        font-size: 1.3rem;
    }

    .island-description {
        font-size: 0.9rem;
    }

    .feature {
        font-size: 0.8rem;
    }
}

@media (max-width: 576px) {
    .islands-grid {
        grid-template-columns: 1fr;
    }

    .island-image {
        height: 160px;
    }

    .island-modal-image-container {
        height: 200px;
    }

    .island-modal-content {
        padding: 20px;
    }

    .island-modal-content h2 {
        font-size: 1.5rem;
    }
}
