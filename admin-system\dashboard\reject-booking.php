<?php
session_start();
include('includes/config.php');
if(strlen($_SESSION['aid'])==0)  {
    header('location:../login/admin-login.php');
    exit();
}
if(isset($_POST['id'])){
    $id = intval($_POST['id']);
    $sql = "UPDATE bookings SET booking_status='rejected' WHERE booking_id=?";
    $stmt = $con->prepare($sql);
    $stmt->bind_param('i', $id);
    $stmt->execute();
    $stmt->close();
    exit();
} else {
    http_response_code(400);
    exit();
}
?>
