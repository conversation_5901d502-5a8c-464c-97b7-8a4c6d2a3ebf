<?php session_start();
// Database Connection
include('includes/config.php');
//Validating Session
if(strlen($_SESSION['aid'])==0)
  { header('location:../login/admin-login.php');
}
else{
// Code for change Password
if(isset($_POST['change'])){
$admid=$_SESSION['aid'];
$cpassword=md5($_POST['currentpassword']);
$newpassword=md5($_POST['newpassword']);
$stmt = $con->prepare("SELECT ID FROM tbladmin WHERE ID=? AND Password=?");
$stmt->bind_param("is", $admid, $cpassword);
$stmt->execute();
$result = $stmt->get_result();
$row = $result->fetch_array();
if($row>0){
$stmt = $con->prepare("UPDATE tbladmin SET Password=? WHERE ID=?");
$stmt->bind_param("si", $newpassword, $admid);
$ret = $stmt->execute();
echo '<script>alert("Your password successully changed.")</script>';
} else {

echo '<script>alert("Your current password is wrong.")</script>';
}



}

// Get date range from POST if submitted
$from_date = isset($_POST['from_date']) ? $_POST['from_date'] : date('Y-m-d');
$to_date = isset($_POST['to_date']) ? $_POST['to_date'] : date('Y-m-d');

// Get statistics
$total_bookings = 0;
$accepted_bookings = 0;
$rejected_bookings = 0;
$total_revenue = 0;

$sql = "SELECT
    COUNT(*) as total,
    SUM(CASE WHEN booking_status = 'accepted' OR booking_status = 'confirmed' THEN 1 ELSE 0 END) as accepted,
    SUM(CASE WHEN booking_status = 'rejected' OR booking_status = 'cancelled' THEN 1 ELSE 0 END) as rejected,
    SUM(CASE WHEN booking_status = 'accepted' OR booking_status = 'confirmed' THEN total ELSE 0 END) as revenue
FROM bookings
WHERE DATE(booking_time) BETWEEN ? AND ?";
$stmt = $con->prepare($sql);
$stmt->bind_param("ss", $from_date, $to_date);
$stmt->execute();
$stats_query = $stmt->get_result();

if($stats_query) {
    $stats = mysqli_fetch_assoc($stats_query);
    $total_bookings = $stats['total'] ?? 0;
    $accepted_bookings = $stats['accepted'] ?? 0;
    $rejected_bookings = $stats['rejected'] ?? 0;
    $total_revenue = $stats['revenue'] ?? 0;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Boat Booking System   | Between Dates Report</title>

  <!-- Google Font: Source Sans Pro -->
  <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
  <!-- Font Awesome -->
  <link rel="stylesheet" href="plugins/fontawesome-free/css/all.min.css">
  <!-- Theme style -->
  <link rel="stylesheet" href="dist/css/adminlte.min.css">
  <!--Function Email Availabilty---->
  <link rel="stylesheet" href="plugins/daterangepicker/daterangepicker.css">
</head>
<body class="hold-transition sidebar-mini">
<div class="wrapper">
  <!-- Navbar -->
<?php include_once("includes/navbar.php");?>
  <!-- /.navbar -->

  <!-- Main Sidebar Container -->
 <?php include_once("includes/sidebar.php");?>

  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6">
            <h1>Between Dates Report</h1>
          </div>
          <div class="col-sm-6">
            <ol class="breadcrumb float-sm-right">
              <li class="breadcrumb-item"><a href="dashboard.php">Dashboard</a></li>
              <li class="breadcrumb-item active">Between Dates Report</li>
            </ol>
          </div>
        </div>
      </div><!-- /.container-fluid -->
    </section>

    <!-- Main content -->
    <section class="content">
      <div class="container-fluid">
        <div class="row">
          <!-- left column -->
          <div class="col-md-12">
            <!-- general form elements -->
            <div class="row mb-4">
  <div class="col-md-3 col-6">
    <div class="small-box bg-info">
      <div class="inner">
        <h3><?php echo $total_bookings; ?></h3>
        <p>Total Bookings</p>
      </div>
      <div class="icon"><i class="fas fa-calendar-check"></i></div>
    </div>
  </div>
  <div class="col-md-3 col-6">
    <div class="small-box bg-success">
      <div class="inner">
        <h3><?php echo $accepted_bookings; ?></h3>
        <p>Accepted</p>
      </div>
      <div class="icon"><i class="fas fa-check-circle"></i></div>
    </div>
  </div>
  <div class="col-md-3 col-6">
    <div class="small-box bg-danger">
      <div class="inner">
        <h3><?php echo $rejected_bookings; ?></h3>
        <p>Rejected</p>
      </div>
      <div class="icon"><i class="fas fa-times-circle"></i></div>
    </div>
  </div>
  <div class="col-md-3 col-6">
    <div class="small-box bg-warning">
      <div class="inner">
        <h3>₱<?php echo number_format($total_revenue, 2); ?></h3>
        <p>Total Revenue</p>
      </div>
      <div class="icon"><i class="fas fa-money-bill-wave"></i></div>
    </div>
  </div>
</div>
<!-- End Summary Cards -->

<div class="card">
  <div class="card-header">
    <h3 class="card-title">Between Dates Booking Report</h3>
  </div>
  <div class="card-body">
    <form method="post" action="" id="reportForm">
      <div class="row">
        <div class="col-md-4">
          <div class="form-group">
            <label>From Date</label>
            <input type="date" class="form-control" name="from_date" value="<?php echo $from_date; ?>" required>
          </div>
        </div>
        <div class="col-md-4">
          <div class="form-group">
            <label>To Date</label>
            <input type="date" class="form-control" name="to_date" value="<?php echo $to_date; ?>" required>
          </div>
        </div>
        <div class="col-md-4">
          <div class="form-group" style="margin-top: 32px;">
            <button type="submit" class="btn btn-primary">
              <i class="fas fa-search"></i> Generate Report
            </button>
            <button type="button" class="btn btn-success" onclick="printReport()">
              <i class="fas fa-print"></i> Print Report
            </button>
          </div>
        </div>
      </div>
    </form>

    <div class="table-responsive mt-4">
      <table class="table table-bordered table-striped" id="reportTable">
        <thead>
          <tr>
            <th>#</th>
            <th>Booking ID</th>
            <th>Customer Name</th>
            <th>Date</th>
            <th>Status</th>
            <th>Amount</th>
          </tr>
        </thead>
        <tbody>
          <?php
          $sql = "SELECT
              b.booking_id,
              b.booking_code,
              CONCAT(c.first_name, ' ', c.last_name) as customer_name,
              b.booking_time,
              b.booking_status,
              b.total
          FROM bookings b
          JOIN customers c ON b.customer_id = c.customer_id
          WHERE DATE(b.booking_time) BETWEEN ? AND ?
          ORDER BY b.booking_time DESC";
$stmt = $con->prepare($sql);
$stmt->bind_param("ss", $from_date, $to_date);
$stmt->execute();
$query = $stmt->get_result();

          if($query) {
              $cnt = 1;
              while($row = mysqli_fetch_assoc($query)) {
                  $status_class = '';
                  $status_text = '';

                  switch($row['booking_status']) {
                      case 'pending':
                          $status_class = 'badge-warning';
                          $status_text = 'Pending';
                          break;
                      case 'accepted':
                      case 'confirmed':
                          $status_class = 'badge-success';
                          $status_text = ucfirst($row['booking_status']);
                          break;
                      case 'rejected':
                      case 'cancelled':
                          $status_class = 'badge-danger';
                          $status_text = ucfirst($row['booking_status']);
                          break;
                      default:
                          $status_class = 'badge-info';
                          $status_text = ucfirst($row['booking_status']);
                  }
                  ?>
                  <tr>
                      <td><?php echo $cnt++; ?></td>
                      <td><?php echo htmlspecialchars($row['booking_code']); ?></td>
                      <td><?php echo htmlspecialchars($row['customer_name']); ?></td>
                      <td><?php echo date('M d, Y', strtotime($row['booking_time'])); ?></td>
                      <td><span class="badge <?php echo $status_class; ?>"><?php echo $status_text; ?></span></td>
                      <td>₱<?php echo number_format($row['total'], 2); ?></td>
                  </tr>
                  <?php
              }
          }
          ?>
        </tbody>
      </table>
    </div>
  </div>
</div>
          </div>
          <!--/.col (left) -->

        </div>
        <!-- /.row -->
      </div><!-- /.container-fluid -->
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php include_once('includes/footer.php');?>

<!-- Notification Modal -->
<div class="modal fade" id="notifModal" tabindex="-1" role="dialog" aria-labelledby="notifModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="notifModalLabel">Notifications</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body" id="notifList">
        <!-- Notifications will be loaded here -->
      </div>
    </div>
  </div>
</div>

<!-- jQuery -->
<script src="plugins/jquery/jquery.min.js"></script>
<!-- Bootstrap 4 -->
<script src="plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
<!-- bs-custom-file-input -->
<script src="plugins/bs-custom-file-input/bs-custom-file-input.min.js"></script>
<!-- AdminLTE App -->
<script src="dist/js/adminlte.min.js"></script>
<!-- AdminLTE for demo purposes -->
<script src="dist/js/demo.js"></script>
<!-- Page specific script -->
<script>
$(function () {
  bsCustomFileInput.init();
});
</script>

<script>
$(document).ready(function() {
  function loadNotifications() {
    $.get('notifications_fetch.php', function(data) {
      let notifs = JSON.parse(data);
      let notifList = $('#notifList');
      let notifCount = notifs.length;
      $('#notifCount').text(notifCount > 0 ? notifCount : '');
      notifList.empty();
      if (notifs.length === 0) {
        notifList.append('<div class="text-center">No new notifications</div>');
      } else {
        notifs.forEach(function(notif) {
          notifList.append('<div class="alert alert-info notif-item" style="cursor:pointer;" onclick="markAsReadAndGo('+notif.id+')">'+notif.message+'</div>');
        });
      }
    });
  }

  // Load notifications when modal is shown
  $('#notifModal').on('show.bs.modal', loadNotifications);
});

function markAsReadAndGo(id) {
  $.get('notifications_mark_read.php?id='+id, function() {
    window.location.href = 'pending-bookings.php';
  });
}
</script>

<script>
$(function () {
    $('#reportTable').DataTable({
        "paging": true,
        "lengthChange": false,
        "searching": true,
        "ordering": true,
        "info": true,
        "autoWidth": false,
        "responsive": true,
    });
});

function printReport() {
    var printWindow = window.open('', '_blank');
    printWindow.document.write('<html><head><title>Booking Report</title>');
    printWindow.document.write('<link rel="stylesheet" href="plugins/bootstrap/css/bootstrap.min.css">');
    printWindow.document.write('<style>@media print { .no-print { display: none; } }</style>');
    printWindow.document.write('</head><body>');

    // Add report header
    printWindow.document.write('<div class="container mt-4">');
    printWindow.document.write('<h2 class="text-center">Booking Report</h2>');
    printWindow.document.write('<p class="text-center">From: <?php echo date("M d, Y", strtotime($from_date)); ?> To: <?php echo date("M d, Y", strtotime($to_date)); ?></p>');

    // Add statistics
    printWindow.document.write('<div class="row mb-4">');
    printWindow.document.write('<div class="col-md-3"><strong>Total Bookings:</strong> <?php echo $total_bookings; ?></div>');
    printWindow.document.write('<div class="col-md-3"><strong>Accepted:</strong> <?php echo $accepted_bookings; ?></div>');
    printWindow.document.write('<div class="col-md-3"><strong>Rejected:</strong> <?php echo $rejected_bookings; ?></div>');
    printWindow.document.write('<div class="col-md-3"><strong>Total Revenue:</strong> ₱<?php echo number_format($total_revenue, 2); ?></div>');
    printWindow.document.write('</div>');

    // Add table
    printWindow.document.write('<table class="table table-bordered">');
    printWindow.document.write('<thead><tr><th>#</th><th>Booking ID</th><th>Customer Name</th><th>Date</th><th>Status</th><th>Amount</th></tr></thead>');
    printWindow.document.write('<tbody>');

    // Add table rows
    <?php
    $sql = "SELECT
        b.booking_id,
        b.booking_code,
        CONCAT(c.first_name, ' ', c.last_name) as customer_name,
        b.booking_time,
        b.booking_status,
        b.total
    FROM bookings b
    JOIN customers c ON b.customer_id = c.customer_id
    WHERE DATE(b.booking_time) BETWEEN ? AND ?
    ORDER BY b.booking_time DESC";
$stmt = $con->prepare($sql);
$stmt->bind_param("ss", $from_date, $to_date);
$stmt->execute();
$query = $stmt->get_result();

    if($query) {
        $cnt = 1;
        while($row = mysqli_fetch_assoc($query)) {
            $status_text = ucfirst($row['booking_status']);
            echo "printWindow.document.write('<tr>');\n";
            echo "printWindow.document.write('<td>".$cnt++."</td>');\n";
            echo "printWindow.document.write('<td>".$row['booking_code']."</td>');\n";
            echo "printWindow.document.write('<td>".$row['customer_name']."</td>');\n";
            echo "printWindow.document.write('<td>".date('M d, Y', strtotime($row['booking_time']))."</td>');\n";
            echo "printWindow.document.write('<td>".$status_text."</td>');\n";
            echo "printWindow.document.write('<td>₱".number_format($row['total'], 2)."</td>');\n";
            echo "printWindow.document.write('</tr>');\n";
        }
    }
    ?>

    printWindow.document.write('</tbody></table>');
    printWindow.document.write('</div>');
    printWindow.document.write('</body></html>');
    printWindow.document.close();
    printWindow.print();
}
</script>
</body>
</html>
<?php } ?>
