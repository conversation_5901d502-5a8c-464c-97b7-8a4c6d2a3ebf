// Beaches Section JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Animate beach features on scroll
    const observerOptions = { root: null, rootMargin: '0px', threshold: 0.1 };
    const observer = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);
    
    document.querySelectorAll('.beach-feature').forEach(element => {
        element.classList.add('hidden');
        observer.observe(element);
    });
    
    // Beach discover button functionality
    document.querySelectorAll('.btn-discover').forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const beachName = this.getAttribute('data-beach');
            if (beachName) {
                // This is a placeholder - in a real implementation, you would show more details about the beach
                alert(`More details about ${beachName} would be shown here.`);
            }
        });
    });
});
