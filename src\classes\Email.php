<?php
class Email {
    private $from;
    private $fromName;
    private $replyTo;
    private $replyToName;

    public function __construct() {
        $this->from = ADMIN_EMAIL;
        $this->fromName = APP_NAME;
        $this->replyTo = ADMIN_EMAIL;
        $this->replyToName = APP_NAME;
    }
    
    public function send($to, $subject, $message) {
        // Set email headers
        $headers = array(
            'MIME-Version: 1.0',
            'Content-type: text/plain; charset=UTF-8',
            'From: ' . $this->fromName . ' <' . $this->from . '>',
            'Reply-To: ' . $this->replyToName . ' <' . $this->replyTo . '>',
            'X-Mailer: PHP/' . phpversion()
        );
        
        // Send email
        return mail($to, $subject, $message, implode("\r\n", $headers));
    }

    public function sendHTML($to, $subject, $htmlMessage) {
        // Set email headers for HTML content
        $headers = array(
            'MIME-Version: 1.0',
            'Content-type: text/html; charset=UTF-8',
            'From: ' . $this->fromName . ' <' . $this->from . '>',
            'Reply-To: ' . $this->replyToName . ' <' . $this->replyTo . '>',
            'X-Mailer: PHP/' . phpversion()
        );

        // Send email
        return mail($to, $subject, $htmlMessage, implode("\r\n", $headers));
    }
} 