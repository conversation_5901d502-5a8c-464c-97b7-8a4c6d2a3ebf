# Online Booking Reservation System: Boat Rental Accommodation for Tourists

## Main System Flow
```
⭮ (START)
   ↓
⏢ [Landing Page]
   ↓
◇ {User Action?}
   ↙ (Book Now)    ↘ (Admin Login)
   ↓               ↓
⬚ [Booking Form]   ◇ {Valid Credentials?}
   ↓               ↙ (No)    ↘ (Yes)
⬚ [Tourist Flow]  [Try Again] [Admin Dashboard]
```

## Tourist Flow
```
⬚ [Tourist Dashboard]
        ↓
◇ {What would you like to do?}
↙ (Book)    ↓ (View)    ↓ (Gallery)    ↘ (Profile)
↓           ↓            ↓               ↓
⬚ Book      ⬚ View       ⬚ Browse       ⬚ Update
Boat        Calendar     Gallery        Profile
↓           ↓            ↓               ↓
◇ {Boats    ◇ {View      ◇ {Filter      ◇ {Save
Available?}  Options?}    Options?}       Changes?}
↙ (No) ↘ (Yes) ↙ (My) ↘ (All) ↙ (Category) ↘ (All) ↙ (No) ↘ (Yes)
Return   Continue  My    Available  Filtered   All    Edit   Save
         ↓         Bookings Dates    Images   Images   ↑     ↓
◇ {Date Available?}                                   Back to
↙ (No)    ↘ (Yes)                                    Dashboard
Choose    Proceed
Another   to Book
```

## Booking Process
```
⬚ [Proceed to Book]
        ↓
⏢ [Enter Personal Details]
        ↓
◇ {Form Complete?}
↙ (No)      ↘ (Yes)
Edit         ↓
Details    ⏢ [Select Destination]
↑           ↓
|         ⏢ [Select Date & Time]
|           ↓
|         ⏢ [Select Boat]
|           ↓
|         ◇ {Confirm Booking?}
|         ↙ (No)    ↘ (Yes)
|      Return    ⏢ [Submit]
|                  ↓
|               ◇ {Email Verification?}
|               ↙ (No)    ↘ (Yes)
|            Resend     ⏢ [Verify Email]
|              ↑          ↓
|              |        ◇ {Payment Method?}
|              |        ↙ (Online)    ↘ (Office)
|              |        ↓              ↓
|              |     ⏢ [Online      ⏢ [Process at Office]
|              |      Payment]        ↓
|              |        ↓           ⏢ [Print Receipt]
|              |        ↓             ↓
|              └────────┴─────────→ ⭮ (END)
```

## Admin System
```
⬚ [Admin Dashboard]
        ↓
◇ {Select Function?}
↙ (Bookings)  ↓ (Boats)   ↓ (Destinations)  ↘ (Reports)
↓             ↓           ↓                  ↓
◇ {Status?}   ◇ {Action?} ◇ {Action?}        ◇ {Type?}
↙   ↓   ↓   ↘ ↙   ↓   ↘   ↙   ↓   ↘         ↙   ↓   ↘
New Pending Accepted All Add Edit View  Add Edit View  Daily Weekly Monthly
↓   ↓      ↓     ↓   ↓   ↓    ↓     ↓    ↓    ↓      ↓     ↓     ↓
⌺ [Database]     ⌺ [Database]      ⌺ [Database]     ⏢ [Generate Report]
        ↘             ↓                 ↓           ↙
         ⭮ (END) ← ───────────────────────────────
```

## Gallery System
```
⬚ [Gallery Page]
        ↓
◇ {Filter Images?}
↙ (All)    ↓ (Islands)    ↓ (Beaches)    ↓ (Activities)    ↘ (Culture)
↓           ↓              ↓               ↓                 ↓
⬚ Show All  ⬚ Show Islands ⬚ Show Beaches ⬚ Show Activities ⬚ Show Culture
Images      Images         Images         Images            Images
↓           ↓              ↓               ↓                 ↓
◇ {Load More Images?}
↙ (No)                  ↘ (Yes)
Return to               ⬚ Load Additional
Current View            Images
```

## Email Verification Flow
```
⬚ [Submit Booking]
        ↓
⏢ [Generate Verification Code]
        ↓
⏢ [Send Email with Code]
        ↓
◇ {Email Received?}
↙ (No)      ↘ (Yes)
Check Spam   ↓
or Resend  ⏢ [Enter Verification Code]
↑           ↓
|         ◇ {Code Valid?}
|         ↙ (No)    ↘ (Yes)
|      Try Again  ⬚ [Booking Confirmed]
|        ↑          ↓
└────────┘        ⭮ (END)
```

## Payment Process
```
⬚ [Verified Booking]
        ↓
◇ {Payment Method?}
↙ (Online)           ↘ (At Office)
↓                     ↓
⏢ [Select Payment    ⏢ [Generate Payment
   Gateway]             Reference]
↓                     ↓
◇ {Payment Complete?} ⬚ [Booking Status: Pending]
↙ (No)    ↘ (Yes)     ↓
Try Again  ↓         ⬚ [Pay at Office]
↑        ⬚ [Booking   ↓
|         Confirmed] ⬚ [Booking Confirmed]
|           ↓         ↓
└───────→ ⭮ (END) ←───
```

## Symbol Legend:
⭮ = Start/End (Oval)
⬚ = Process (Rectangle)
◇ = Decision (Diamond)
⏢ = Input/Output (Parallelogram)
⌺ = Database (Cylinder)
