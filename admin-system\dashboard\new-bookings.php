<?php session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);
// Database Connection -ramos
include('includes/config.php');
//Validating Session
if(strlen($_SESSION['aid'])==0)
  {
header('location:../login/admin-login.php');
}
else{


  ?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title> Booking System | New Bookings</title>

  <!-- Google Font: Source Sans Pro -->
  <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
  <!-- Font Awesome -->
  <link rel="stylesheet" href="plugins/fontawesome-free/css/all.min.css">
  <!-- DataTables -->
  <link rel="stylesheet" href="plugins/datatables-bs4/css/dataTables.bootstrap4.min.css">
  <link rel="stylesheet" href="plugins/datatables-responsive/css/responsive.bootstrap4.min.css">
  <link rel="stylesheet" href="plugins/datatables-buttons/css/buttons.bootstrap4.min.css">
  <!-- Theme style -->
  <link rel="stylesheet" href="dist/css/adminlte.min.css">
</head>
<body class="hold-transition sidebar-mini">
<div class="wrapper">
  <!-- Navbar -->
<?php include_once("includes/navbar.php");?>
  <!-- /.navbar -->

 <?php include_once("includes/sidebar.php");?>

  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6">
            <h1>New Bookings</h1>
          </div>
          <div class="col-sm-6">
            <ol class="breadcrumb float-sm-right">
              <li class="breadcrumb-item"><a href="dashboard.php">Home</a></li>
              <li class="breadcrumb-item active">New Bookings</li>
            </ol>
          </div>
        </div>
      </div><!-- /.container-fluid -->
    </section>

    <!-- Main content -->
    <section class="content">
      <div class="container-fluid">
        <div class="row">
          <div class="col-12">
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">Bookings Details</h3>
              </div>
              <!-- /.card-header -->
              <div class="card-body">
                <table id="example1" class="table table-bordered table-striped">
                  <thead>
                  <tr>
                    <th>#</th>
                    <th>Reference No</th>
                    <th>Customer Name</th>
                    <th>Email</th>
                    <th>Contact No</th>
                    <th>Emergency Name</th>
                    <th>Emergency No</th>
                    <th>No. of People</th>
                    <th>Boat</th>
                    <th>Destination</th>
                    <th>Drop-off Location</th>
                    <th>Date</th>
                    <th>Time</th>
                    <th>Status</th>
                    <th>Action</th>
                  </tr>
                  </thead>
                  <tbody>
<?php
// Use correct table and columns for new bookings with prepared statement
// First update the dates for any bookings with future dates (2025)
$update_sql = "UPDATE bookings
SET
    start_date = DATE_SUB(NOW(), INTERVAL 1 DAY),
    end_date = DATE_ADD(NOW(), INTERVAL 1 DAY),
    booking_time = DATE_SUB(NOW(), INTERVAL 2 DAY),
    created_at = DATE_SUB(NOW(), INTERVAL 3 DAY)
WHERE
    booking_status = 'pending' AND YEAR(created_at) = 2025";
$con->query($update_sql);

// Also update confirmed bookings with future dates
$update_confirmed_sql = "UPDATE bookings
SET
    start_date = DATE_SUB(NOW(), INTERVAL 5 DAY),
    end_date = DATE_SUB(NOW(), INTERVAL 4 DAY),
    booking_time = DATE_SUB(NOW(), INTERVAL 7 DAY),
    created_at = DATE_SUB(NOW(), INTERVAL 8 DAY)
WHERE
    booking_status = 'confirmed' AND YEAR(created_at) = 2025";
$con->query($update_confirmed_sql);

// Also update cancelled bookings with future dates
$update_cancelled_sql = "UPDATE bookings
SET
    start_date = DATE_SUB(NOW(), INTERVAL 10 DAY),
    end_date = DATE_SUB(NOW(), INTERVAL 9 DAY),
    booking_time = DATE_SUB(NOW(), INTERVAL 12 DAY),
    created_at = DATE_SUB(NOW(), INTERVAL 13 DAY)
WHERE
    booking_status = 'cancelled' AND YEAR(created_at) = 2025";
$con->query($update_cancelled_sql);

// Show only PENDING bookings
$sql = "SELECT
                            b.booking_id,
                            b.booking_code,
                            b.first_name,
                            b.last_name,
                            b.email,
                            b.contact_number,
                            b.emergency_name,
                            b.emergency_number,
                            b.no_of_pax,
                            b.start_date,
                            b.created_at as booking_time,
                            b.total as total,
                            b.booking_status,
                            'Assigned by Tourism Office' AS boat_name, /* Tourism Office will arrange the boat */
                            b.tour_destination AS destination_name,
                            b.drop_off_location
                        FROM bookings b
                        WHERE b.booking_status = 'pending'
                        ORDER BY b.created_at DESC";

// Log the query for debugging
error_log("Showing only PENDING bookings in New Bookings page");
$stmt = $con->prepare($sql);
$stmt->execute();
$query = $stmt->get_result();

if (!$query) { die('Query Error: ' . mysqli_error($con)); }
$cnt = 1;
$hasRows = false;
while ($row = mysqli_fetch_array($query)) {
  $hasRows = true;

  // Set status class
  $status_class = "bg-warning";
  $status_text = "PENDING";
?>
                        <tr>
                            <td><?php echo $cnt++; ?></td>
                            <td><?php echo htmlspecialchars($row['booking_code']); ?></td>
                            <td><?php echo htmlspecialchars($row['first_name'] ?? '') . ' ' . htmlspecialchars($row['last_name'] ?? ''); ?></td>
                            <td><?php echo htmlspecialchars($row['email'] ?? ''); ?></td>
                            <td><?php echo htmlspecialchars($row['contact_number'] ?? ''); ?></td>
                            <td><?php echo htmlspecialchars($row['emergency_name'] ?? 'Not set'); ?></td>
                            <td><?php echo htmlspecialchars($row['emergency_number'] ?? 'Not set'); ?></td>
                            <td><?php echo htmlspecialchars($row['no_of_pax'] ?? ''); ?></td>
                            <td><?php echo htmlspecialchars($row['boat_name'] ?? ''); ?></td>
                            <td><?php echo htmlspecialchars($row['destination_name'] ?? ''); ?></td>
                            <td><?php echo htmlspecialchars($row['drop_off_location'] ?? 'Not set'); ?></td>
                            <td><?php echo !empty($row['start_date']) ? date('M d, Y', strtotime($row['start_date'])) : 'N/A'; ?></td>
                            <td><?php echo !empty($row['booking_time']) ? date('H:i', strtotime($row['booking_time'])) : 'N/A'; ?></td>
                            <td><span class="badge <?php echo $status_class; ?>"><?php echo $status_text; ?></span></td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button class="btn btn-info btn-sm view-btn" data-id="<?php echo $row['booking_id']; ?>" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-success btn-sm accept-btn" data-id="<?php echo $row['booking_id']; ?>" title="Accept Booking">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    <button class="btn btn-danger btn-sm reject-btn" data-id="<?php echo $row['booking_id']; ?>" title="Reject Booking">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
         <?php }
if (!$hasRows) {
  echo '<tr><td colspan="15" class="text-center"><i class="fas fa-info-circle"></i> No new bookings found.</td></tr>';
}
?>
                </table>
              </div>
              <!-- /.card-body -->
            </div>
            <!-- /.card -->
          </div>
          <!-- /.col -->
        </div>
        <!-- /.row -->
      </div>
      <!-- /.container-fluid -->
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->

  <!-- Action Modal -->
  <div class="modal fade" id="actionModal" tabindex="-1" role="dialog" aria-labelledby="actionModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="actionModalLabel">Confirm Action</h5>
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body" id="modalMessage"></div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
          <button type="button" class="btn btn-primary" id="modalOkBtn">Confirm</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Booking Details Modal -->
  <div class="modal fade" id="bookingDetailsModal" tabindex="-1" role="dialog" aria-labelledby="bookingDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
      <div class="modal-content">
        <div class="modal-header bg-info">
          <h5 class="modal-title text-white" id="bookingDetailsModalLabel">
            <i class="fas fa-info-circle"></i> Booking Details
          </h5>
          <div>
            <button type="button" class="btn btn-light btn-sm mr-2" id="printBookingDetails">
              <i class="fas fa-print"></i> Print
            </button>
            <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
        </div>
        <div class="modal-body">
          <div class="table-responsive">
            <table class="table table-bordered table-striped" id="bookingDetailsTable">
              <tr><th>First Name:</th><td id="viewFirstName"></td></tr>
              <tr><th>Last Name:</th><td id="viewLastName"></td></tr>
              <tr><th>Age:</th><td id="viewAge"></td></tr>
              <tr><th>Sex:</th><td id="viewSex"></td></tr>
              <tr><th>Contact Number:</th><td id="viewContactNumber"></td></tr>
              <tr><th>Email:</th><td id="viewEmail"></td></tr>
              <tr><th>Address:</th><td id="viewAddress"></td></tr>
              <tr><th>Emergency Contact Name:</th><td id="viewEmergencyName"></td></tr>
              <tr><th>Emergency Contact Number:</th><td id="viewEmergencyNumber"></td></tr>
              <tr><th>Tour Destination:</th><td id="viewDestination"></td></tr>
              <tr><th>Drop-off Location:</th><td id="viewDropoffLocation"></td></tr>
              <tr><th>Number of Pax:</th><td id="viewNoOfPax"></td></tr>
              <tr><th>Start Date:</th><td id="viewStartDate"></td></tr>
              <tr><th>End Date:</th><td id="viewEndDate"></td></tr>
              <tr><th>Booking Time:</th><td id="viewBookingTime"></td></tr>
              <tr><th>Selected Boat:</th><td id="viewBoat"></td></tr>
              <tr><th>Boat Price:</th><td id="viewBoatPrice"></td></tr>
              <tr><th>Booking Code:</th><td id="viewBookingCode"></td></tr>
              <tr><th>Environmental Fee:</th><td id="viewEnvironmentalFee"></td></tr>
              <tr><th>Payment Method:</th><td id="viewPaymentMethod"></td></tr>
              <tr><th>Total Amount:</th><td id="viewTotal"></td></tr>
            </table>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
        </div>
      </div>
    </div>
  </div>

<?php include_once('includes/footer.php');?>

  <!-- Control Sidebar -->
  <aside class="control-sidebar control-sidebar-dark">
    <!-- Control sidebar content goes here -->
  </aside>
  <!-- /.control-sidebar -->
</div>
<!-- ./wrapper -->

<!-- jQuery -->
<script src="plugins/jquery/jquery.min.js"></script>
<!-- Bootstrap 4 -->
<script src="plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
<!-- DataTables  & Plugins -->
<script src="plugins/datatables/jquery.dataTables.min.js"></script>
<script src="plugins/datatables-bs4/js/dataTables.bootstrap4.min.js"></script>
<script src="plugins/datatables-responsive/js/dataTables.responsive.min.js"></script>
<script src="plugins/datatables-responsive/js/responsive.bootstrap4.min.js"></script>
<script src="plugins/datatables-buttons/js/dataTables.buttons.min.js"></script>
<script src="plugins/datatables-buttons/js/buttons.bootstrap4.min.js"></script>
<script src="plugins/jszip/jszip.min.js"></script>
<script src="plugins/pdfmake/pdfmake.min.js"></script>
<script src="plugins/pdfmake/vfs_fonts.js"></script>
<script src="plugins/datatables-buttons/js/buttons.html5.min.js"></script>
<script src="plugins/datatables-buttons/js/buttons.print.min.js"></script>
<script src="plugins/datatables-buttons/js/buttons.colVis.min.js"></script>
<!-- AdminLTE App -->
<script src="dist/js/adminlte.min.js"></script>
<!-- AdminLTE for demo purposes -->
<script src="dist/js/demo.js"></script>
<!-- Page specific script -->
<script>
$(function() {
  // Check if DataTable is already initialized
  if ($.fn.dataTable.isDataTable('#example1')) {
    // If already initialized, destroy it first
    $('#example1').DataTable().destroy();
  }

  // Initialize DataTable
  var table = $('#example1').DataTable({
    "responsive": true,
    "lengthChange": true,
    "autoWidth": false,
    "buttons": ["copy", "csv", "excel", "pdf", "print"],
    "order": [[0, "desc"]],
    "pageLength": 10,
    "language": {
      "search": "Search bookings:",
      "lengthMenu": "Show _MENU_ entries",
      "info": "Showing _START_ to _END_ of _TOTAL_ entries",
      "infoEmpty": "No entries found",
      "infoFiltered": "(filtered from _MAX_ total entries)"
    }
  }).buttons().container().appendTo('#example1_wrapper .col-md-6:eq(0)');

  // View booking details
  $(document).on('click', '.view-btn', function(e) {
    e.preventDefault();
    var id = $(this).data('id');
    console.log('View button clicked for ID:', id);

    $.ajax({
      url: 'get-booking-details.php',
      type: 'POST',
      data: { id: id },
      dataType: 'json',
      success: function(response) {
        console.log('Response:', response);
        if(response.success && response.data) {
          var booking = response.data;

          // Format dates
          var startDate = new Date(booking.start_date);
          var endDate = new Date(booking.end_date);

          // Format booking time properly
          var bookingTime = booking.booking_time;
          var formattedTime = '';
          if (bookingTime) {
            // If booking_time is in HH:mm format
            if (bookingTime.match(/^\d{2}:\d{2}$/)) {
              formattedTime = bookingTime;
            } else {
              // Try to parse as a date
              var timeDate = new Date(bookingTime);
              if (!isNaN(timeDate.getTime())) {
                formattedTime = timeDate.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
              } else {
                formattedTime = 'Not set';
              }
            }
          } else {
            formattedTime = 'Not set';
          }

          // Format currency
          function formatPeso(amount) {
            // Use toLocaleString for better formatting without dollar signs
            return '₱ ' + parseFloat(amount).toLocaleString('en-PH', {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2
            });
          }

          // Set values
          $('#viewFirstName').text(booking.first_name || 'Not set');
          $('#viewLastName').text(booking.last_name || 'Not set');
          $('#viewAge').text(booking.age || 'Not set');
          $('#viewSex').text(booking.sex || 'Not set');
          $('#viewContactNumber').text(booking.contact_number || 'Not set');
          $('#viewEmail').text(booking.email || 'Not set');
          $('#viewAddress').text(booking.address || 'Not set');
          $('#viewEmergencyName').text(booking.emergency_name || 'Not set');
          $('#viewEmergencyNumber').text(booking.emergency_number || 'Not set');
          $('#viewDestination').text(booking.destination_name || 'Not set');
          $('#viewDropoffLocation').text(booking.drop_off_location || 'Not set');
          $('#viewNoOfPax').text(booking.no_of_pax || 'Not set');
          $('#viewStartDate').text(booking.start_date ? new Date(booking.start_date).toLocaleDateString() : 'Not set');
          $('#viewEndDate').text(booking.end_date ? new Date(booking.end_date).toLocaleDateString() : 'Not set');
          $('#viewBookingTime').text(formattedTime);
          $('#viewBoat').text(booking.boat_name || 'Not set');
          $('#viewBoatPrice').text(booking.price_per_day ? formatPeso(booking.price_per_day) + ' per day' : 'Not set');
          $('#viewBookingCode').text(booking.booking_code || 'Not set');
          $('#viewEnvironmentalFee').text(booking.environmental_fee ? formatPeso(booking.environmental_fee) : 'Not set');
          $('#viewPaymentMethod').text(booking.payment_method ? booking.payment_method.toUpperCase() : 'Not set');
          $('#viewTotal').text(booking.total ? formatPeso(booking.total) : 'Not set');

          // Show the modal
          $('#bookingDetailsModal').modal('show');
        } else {
          alert('Error loading booking details: ' + (response.error || 'Unknown error'));
        }
      },
      error: function(xhr, status, error) {
        console.error('Error:', error);
        console.error('Status:', status);
        console.error('Response:', xhr.responseText);
        alert('Error loading booking details. Please try again.');
      }
    });
  });

  // Accept booking
  $(document).on('click', '.accept-btn', function() {
    var id = $(this).data('id');
    $('#modalMessage').html(`
      <div class="alert alert-success">
        <i class="fas fa-check-circle"></i> Are you sure you want to accept this booking?
        <br><small class="text-muted">This will change the booking status to "Confirmed".</small>
      </div>
    `);
    $('#actionModal').modal('show');

    $('#modalOkBtn').off('click').on('click', function() {
      // Get customer name for this booking
      var customerName = '';
      $.ajax({
        url: 'get-booking-details.php',
        type: 'POST',
        data: { id: id },
        dataType: 'json',
        async: false,
        success: function(response) {
          if(response.success && response.data) {
            customerName = (response.data.first_name || '') + ' ' + (response.data.last_name || '');
            console.log('Got customer name:', customerName);
          }
        }
      });

      $.ajax({
        url: 'confirm-booking.php',
        type: 'POST',
        data: {
          id: id,
          action: 'confirm',
          customer_name: customerName
        },
        dataType: 'json',
        success: function(response) {
          if(response.success) {
            $('#modalMessage').html(`
              <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> Booking accepted successfully.
              </div>
            `);
            $('#modalOkBtn').text('Close').off('click').on('click', function() {
              window.location.reload();
            });
          } else {
            $('#modalMessage').html(`
              <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i> Error: ${response.error || 'Failed to accept booking'}
              </div>
            `);
          }
        },
        error: function() {
          $('#modalMessage').html(`
            <div class="alert alert-danger">
              <i class="fas fa-exclamation-triangle"></i> An error occurred while processing the booking.
            </div>
          `);
        }
      });
    });
  });

  // Reject booking
  $(document).on('click', '.reject-btn', function() {
    var id = $(this).data('id');
    $('#modalMessage').html(`
      <div class="alert alert-danger">
        <i class="fas fa-exclamation-triangle"></i> Are you sure you want to reject this booking?
        <br><small class="text-muted">This will change the booking status to "Cancelled".</small>
      </div>
    `);
    $('#actionModal').modal('show');

    $('#modalOkBtn').off('click').on('click', function() {
      // Get customer name for this booking
      var customerName = '';
      $.ajax({
        url: 'get-booking-details.php',
        type: 'POST',
        data: { id: id },
        dataType: 'json',
        async: false,
        success: function(response) {
          if(response.success && response.data) {
            customerName = (response.data.first_name || '') + ' ' + (response.data.last_name || '');
            console.log('Got customer name for reject:', customerName);
          }
        }
      });

      $.ajax({
        url: 'confirm-booking.php',
        type: 'POST',
        data: {
          id: id,
          action: 'cancel',
          customer_name: customerName
        },
        dataType: 'json',
        success: function(response) {
          if(response.success) {
            $('#modalMessage').html(`
              <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> Booking rejected successfully.
              </div>
            `);
            $('#modalOkBtn').text('Close').off('click').on('click', function() {
              window.location.reload();
            });
          } else {
            $('#modalMessage').html(`
              <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i> Error: ${response.error || 'Failed to reject booking'}
              </div>
            `);
          }
        },
        error: function() {
          $('#modalMessage').html(`
            <div class="alert alert-danger">
              <i class="fas fa-exclamation-triangle"></i> An error occurred while processing the booking.
            </div>
          `);
        }
      });
    });
  });

  // Print booking details if needed - ramos
  $('#printBookingDetails').click(function() {
    var bookingData = {
      firstName: $('#viewFirstName').text(),
      lastName: $('#viewLastName').text(),
      age: $('#viewAge').text(),
      sex: $('#viewSex').text(),
      contactNumber: $('#viewContactNumber').text(),
      email: $('#viewEmail').text(),
      address: $('#viewAddress').text(),
      emergencyName: $('#viewEmergencyName').text(),
      emergencyNumber: $('#viewEmergencyNumber').text(),
      destination: $('#viewDestination').text(),
      dropOffLocation: $('#viewDropoffLocation').text(),

      noOfPax: $('#viewNoOfPax').text(),
      startDate: $('#viewStartDate').text(),
      endDate: $('#viewEndDate').text(),
      bookingTime: $('#viewBookingTime').text(),
      boat: $('#viewBoat').text(),
      boatPrice: $('#viewBoatPrice').text(),
      bookingCode: $('#viewBookingCode').text(),
      environmentalFee: $('#viewEnvironmentalFee').text(),
      paymentMethod: $('#viewPaymentMethod').text(),
      total: $('#viewTotal').text()
    };

    var printContent = `
      <div id="printSection">
        <div class="print-header">
          <img src="images/timbook-carles-tourism.png" alt="Timbook Carles Tourism">
          <h2>Booking Details</h2>
          <p>Carles Tourism</p>
          <div class="booking-code">Booking Code: ${bookingData.bookingCode}</div>
        </div>
        <div class="print-content">
          <div class="print-row">
            <div class="print-label">Name:</div>
            <div class="print-value">${bookingData.firstName} ${bookingData.lastName}</div>
          </div>
          <div class="print-row">
            <div class="print-label">Age:</div>
            <div class="print-value">${bookingData.age}</div>
          </div>
          <div class="print-row">
            <div class="print-label">Sex:</div>
            <div class="print-value">${bookingData.sex}</div>
          </div>
          <div class="print-row">
            <div class="print-label">Contact:</div>
            <div class="print-value">${bookingData.contactNumber}</div>
          </div>
          <div class="print-row">
            <div class="print-label">Email:</div>
            <div class="print-value">${bookingData.email}</div>
          </div>
          <div class="print-row">
            <div class="print-label">Address:</div>
            <div class="print-value">${bookingData.address}</div>
          </div>
          <div class="print-row">
            <div class="print-label">Emergency Name:</div>
            <div class="print-value">${bookingData.emergencyName}</div>
          </div>
          <div class="print-row">
            <div class="print-label">Emergency Number:</div>
            <div class="print-value">${bookingData.emergencyNumber}</div>
          </div>
          <div class="print-row">
            <div class="print-label">Destination:</div>
            <div class="print-value">${bookingData.destination}</div>
          </div>
          <div class="print-row">
            <div class="print-label">Drop-off Location:</div>
            <div class="print-value">${bookingData.dropOffLocation}</div>
          </div>

          <div class="print-row">
            <div class="print-label">No. of Pax:</div>
            <div class="print-value">${bookingData.noOfPax}</div>
          </div>
          <div class="print-row">
            <div class="print-label">Start Date:</div>
            <div class="print-value">${bookingData.startDate}</div>
          </div>
          <div class="print-row">
            <div class="print-label">End Date:</div>
            <div class="print-value">${bookingData.endDate}</div>
          </div>
          <div class="print-row">
            <div class="print-label">Time:</div>
            <div class="print-value">${bookingData.bookingTime}</div>
          </div>
          <div class="print-row">
            <div class="print-label">Boat:</div>
            <div class="print-value">${bookingData.boat}</div>
          </div>
          <div class="print-row">
            <div class="print-label">Boat Price:</div>
            <div class="print-value">${bookingData.boatPrice}</div>
          </div>
          <div class="print-row">
            <div class="print-label">Env. Fee:</div>
            <div class="print-value">${bookingData.environmentalFee}</div>
          </div>
          <div class="print-row">
            <div class="print-label">Payment:</div>
            <div class="print-value">${bookingData.paymentMethod}</div>
          </div>
          <div class="print-row">
            <div class="print-label">Total:</div>
            <div class="print-value">${bookingData.total}</div>
          </div>
        </div>
        <div class="print-footer">
          <p>Thank you for choosing Carles Tourism!</p>
          <p>This is a computer-generated document.</p>
        </div>
      </div>
    `;

    var originalContents = document.body.innerHTML;
    document.body.innerHTML = printContent;
    window.print();
    document.body.innerHTML = originalContents;

    // Reinitialize DataTable and other scripts
    $('#example1').DataTable();
  });
});
</script>

<!-- Add this style section for printing -->
<style>
@media print {
  body * {
    visibility: hidden;
  }
  #printSection, #printSection * {
    visibility: visible;
  }
  #printSection {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    padding: 20px;
  }
  .modal-footer, .modal-header {
    display: none !important;
  }
  .print-header {
    text-align: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #000;
  }
  .print-header img {
    height: 60px;
    margin-bottom: 10px;
  }
  .print-header h2 {
    margin: 5px 0;
    font-size: 20px;
  }
  .print-header p {
    margin: 5px 0;
    font-size: 14px;
  }
  .print-content {
    padding: 10px;
    font-size: 13px;
  }
  .print-row {
    margin-bottom: 5px;
    padding: 3px 0;
  }
  .print-label {
    font-weight: bold;
    display: inline-block;
    width: 150px;
  }
  .print-value {
    display: inline-block;
  }
  .print-footer {
    text-align: center;
    margin-top: 20px;
    padding-top: 10px;
    border-top: 1px solid #000;
    font-size: 12px;
  }
  .booking-code {
    font-size: 16px;
    font-weight: bold;
    margin: 5px 0;
  }
}
</style>
</body>
</html>
<?php } ?> <!--Ramos-->