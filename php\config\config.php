<?php
/**
 * Main Configuration File
 * Carles Tourism Booking System
 */

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Timezone
date_default_timezone_set('Asia/Manila');

// Session configuration
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Database configuration
define('DB_HOST', 'localhost');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_NAME', 'booking_system');

// Base URL
if (!defined('BASE_URL')) {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
    $host = $_SERVER['HTTP_HOST'];
    $path = '/Online Booking Reservation System';
    define('BASE_URL', $protocol . $host . $path . '/');
}

// Site configuration
define('SITE_NAME', 'Carles Tourism');
define('SITE_DESCRIPTION', 'Discover the beautiful islands of Carles');

// Email configuration
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-app-password');

// Security settings
define('HASH_ALGO', 'sha256');
define('SESSION_TIMEOUT', 3600); // 1 hour
?>
