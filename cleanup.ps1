# Carles Tourism System Cleanup Script
# Run this AFTER testing the new organization

Write-Host "🧹 Starting Carles Tourism System Cleanup..." -ForegroundColor Green

# Function to safely remove files/folders
function Safe-Remove {
    param($Path, $Description)
    
    if (Test-Path $Path) {
        Write-Host "🗑️  Removing $Description..." -ForegroundColor Yellow
        Remove-Item $Path -Recurse -Force
        Write-Host "✅ Removed: $Path" -ForegroundColor Green
    } else {
        Write-Host "⚠️  Not found: $Path" -ForegroundColor Gray
    }
}

# Remove duplicate class folders (after confirming new structure works)
Write-Host "`n📁 Cleaning up duplicate class files..." -ForegroundColor Cyan
Safe-Remove "classes" "Old classes folder"
Safe-Remove "php\classes" "PHP classes folder"

# Remove old CSS folder (after updating paths)
Write-Host "`n🎨 Cleaning up old CSS files..." -ForegroundColor Cyan
Safe-Remove "css" "Old CSS folder"

# Remove old JS folder (after updating paths)
Write-Host "`n📜 Cleaning up old JavaScript files..." -ForegroundColor Cyan
Safe-Remove "js" "Old JavaScript folder"

# Remove old image folder (after updating paths)
Write-Host "`n🖼️  Cleaning up old image files..." -ForegroundColor Cyan
Safe-Remove "img" "Old images folder"

# Remove old log folder
Write-Host "`n📋 Cleaning up old log files..." -ForegroundColor Cyan
Safe-Remove "logs" "Old logs folder"

# Remove old SQL folder
Write-Host "`n🗄️  Cleaning up old SQL files..." -ForegroundColor Cyan
Safe-Remove "sql" "Old SQL folder"

# Remove backup and temporary files
Write-Host "`n🔄 Cleaning up backup and temporary files..." -ForegroundColor Cyan
Safe-Remove "process\send_verification_email.php.bak" "Backup verification email file"
Safe-Remove "process\send_verification_email.php.new" "New verification email file"
Safe-Remove "userinput.py" "Python input file"
Safe-Remove "REST.txt" "REST text file"

# Remove old documentation files (moved to docs/)
Write-Host "`n📚 Cleaning up old documentation..." -ForegroundColor Cyan
Safe-Remove "project_structure.md" "Old project structure file"
Safe-Remove "system_flowchart.md" "Old system flowchart"
Safe-Remove "system_flowchart_updated.md" "Old updated flowchart"
Safe-Remove "REORGANIZATION_PLAN.md" "Old reorganization plan"

# Remove unused HTML files
Write-Host "`n🌐 Cleaning up unused HTML files..." -ForegroundColor Cyan
Safe-Remove "html" "HTML folder"

# Remove old config duplicates (keep main config folder)
Write-Host "`n⚙️  Cleaning up duplicate config files..." -ForegroundColor Cyan
Safe-Remove "php\config" "PHP config folder"

Write-Host "`n✨ Cleanup completed!" -ForegroundColor Green
Write-Host "📋 Summary:" -ForegroundColor Cyan
Write-Host "   • Removed duplicate class files" -ForegroundColor White
Write-Host "   • Cleaned up old CSS/JS/Image folders" -ForegroundColor White
Write-Host "   • Removed backup and temporary files" -ForegroundColor White
Write-Host "   • Organized documentation" -ForegroundColor White
Write-Host "   • Removed unused HTML files" -ForegroundColor White

Write-Host "`n⚠️  IMPORTANT: Test the new structure before running this cleanup!" -ForegroundColor Red
Write-Host "📁 New structure is in:" -ForegroundColor Yellow
Write-Host "   • public/ - Web accessible files" -ForegroundColor White
Write-Host "   • src/ - Source code" -ForegroundColor White
Write-Host "   • database/ - Database files" -ForegroundColor White
Write-Host "   • storage/ - Logs and storage" -ForegroundColor White
Write-Host "   • docs/ - Documentation" -ForegroundColor White
