/* Beaches Section Styles */
.beaches-section {
    padding: 80px 0;
    background: #ffffff;
    position: relative;
    overflow: hidden;
}

.beaches-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('../../img/wave-pattern.png') repeat;
    opacity: 0.05;
    z-index: 0;
}

.beaches-section .section-header {
    text-align: center;
    margin-bottom: 50px;
    position: relative;
    z-index: 1;
}

.beaches-section .section-title {
    color: #333333;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 15px;
    text-shadow: none;
}

.beaches-section .section-title::after {
    content: '';
    display: block;
    margin: 18px auto 0 auto;
    width: 80px;
    height: 3px;
    background: linear-gradient(90deg, #3cff00, #1261d0);
    border-radius: 2px;
}

.beaches-section .section-subtitle {
    color: #666666;
    font-size: 1.1rem;
    max-width: 600px;
    margin: 0 auto;
    opacity: 0.95;
}

.beaches-showcase {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 30px;
    position: relative;
    z-index: 1;
}

.beach-feature {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.beach-feature:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
    border-color: rgba(255, 255, 255, 0.2);
}

.beach-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.beach-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.beach-feature:hover .beach-image img {
    transform: scale(1.1);
}

.beach-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.7));
    display: flex;
    align-items: flex-end;
    padding: 20px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.beach-feature:hover .beach-overlay {
    opacity: 1;
}

.beach-info {
    color: #ffffff;
}

.beach-info h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 5px;
    text-shadow: 1px 1px 3px rgba(0,0,0,0.5);
}

.beach-info p {
    font-size: 0.95rem;
    opacity: 0.9;
}

.beach-details {
    padding: 20px;
}

.beach-highlights {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.highlight {
    display: flex;
    align-items: center;
    gap: 5px;
    background: rgba(255, 255, 255, 0.1);
    padding: 5px 10px;
    border-radius: 5px;
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.9);
}

.highlight i {
    color: #4CAF50;
}

/* Responsive Styles */
@media (max-width: 991px) {
    .beaches-section {
        padding: 60px 0;
    }
    
    .beaches-section .section-title {
        font-size: 2rem;
    }
    
    .beaches-section .section-subtitle {
        font-size: 1rem;
    }
    
    .beaches-showcase {
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 20px;
    }
    
    .beach-image {
        height: 200px;
    }
}

@media (max-width: 767px) {
    .beaches-section {
        padding: 50px 0;
    }
    
    .beaches-section .section-title {
        font-size: 1.8rem;
    }
    
    .beaches-showcase {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 15px;
    }
    
    .beach-info h3 {
        font-size: 1.3rem;
    }
    
    .beach-info p {
        font-size: 0.9rem;
    }
    
    .highlight {
        font-size: 0.8rem;
    }
}

@media (max-width: 576px) {
    .beaches-showcase {
        grid-template-columns: 1fr;
    }
    
    .beach-image {
        height: 180px;
    }
}
