// Main JavaScript for Online Booking Reservation Boat Rentals and Accommodation
// Includes animations, interactions, and dynamic elements

document.addEventListener('DOMContentLoaded', function() {
    // --- Swiper Initialization for Gallery ---
    if (typeof Swiper !== 'undefined') {
        new Swiper('.gallery-swiper', {
            slidesPerView: 3,
            spaceBetween: 20,
            loop: true,
            centeredSlides: false,
            autoplay: { delay: 5000, disableOnInteraction: false },
            pagination: { el: '.gallery-pagination', clickable: true },
            navigation: { nextEl: '.gallery-button-next', prevEl: '.gallery-button-prev' },
            breakpoints: { 640: { slidesPerView: 2 }, 768: { slidesPerView: 3 } }
        });
    }

    // --- Navbar Scroll & Active Link ---
    const mainNavLinks = document.querySelectorAll('.nav-link:not(.social-icon):not(.book-now):not(.login-btn)');
    const sections = Array.from(document.querySelectorAll('section[id]'));
    mainNavLinks.forEach(link => {
        link.addEventListener('click', function() {
            mainNavLinks.forEach(l => l.classList.remove('active'));
            this.classList.add('active');
        });
    });
    window.addEventListener('scroll', function() {
        let scrollPos = window.scrollY + 120;
        let found = false;
        sections.forEach(section => {
            if (scrollPos >= section.offsetTop && scrollPos < section.offsetTop + section.offsetHeight) {
                mainNavLinks.forEach(link => {
                    link.classList.remove('active');
                    if (link.getAttribute('href').includes('#' + section.id)) {
                        link.classList.add('active');
                        found = true;
                    }
                });
            }
        });
        if (!found && window.scrollY < 200) {
            mainNavLinks.forEach(link => link.classList.remove('active'));
            const homeLink = document.querySelector('.nav-link[href="online-booking.php"]');
            if (homeLink) homeLink.classList.add('active');
        }
    });

    // --- Header Transparency ---
    const header = document.querySelector('.site-header');
    if (header) {
        header.classList.add('transparent');
        window.addEventListener('scroll', function() {
            if (window.scrollY > 50) {
                header.classList.remove('transparent');
                header.classList.add('scrolled');
            } else {
                header.classList.remove('scrolled');
                header.classList.add('transparent');
            }
        });
    }

    // --- Social Media Icons Animation ---
    document.querySelectorAll('.social-icon').forEach(icon => {
        // Make sure social icons are clickable
        icon.style.pointerEvents = 'auto';

        // Only add animation for search icon, let others navigate normally
        if (icon.classList.contains('search-toggle')) {
            icon.addEventListener('click', function(e) {
                e.preventDefault();
                document.querySelectorAll('.social-icon').forEach(i => i.classList.remove('active'));
                this.classList.add('active');
                setTimeout(() => { this.classList.remove('active'); }, 500);
            });
        } else {
            // For social media links, just add visual feedback
            icon.addEventListener('click', function() {
                // Let the default link behavior happen (navigation)
                console.log('Social link clicked:', this.getAttribute('href'));
            });
        }
    });

    // --- Search Icon Animation ---
    const searchIcon = document.querySelector('.search-toggle');
    if (searchIcon) {
        searchIcon.addEventListener('click', function(e) {
            e.preventDefault();
            this.classList.add('active');
            setTimeout(() => { this.classList.remove('active'); }, 500);
        });
    }

    // --- Dropdown Menu Functionality ---
    document.querySelectorAll('.nav-dropdown').forEach(dropdown => {
        const menu = dropdown.querySelector('.nav-dropdown-menu');
        if (!menu) return;
        if (window.innerWidth > 768) {
            dropdown.addEventListener('mouseenter', () => {
                menu.style.opacity = '1';
                menu.style.visibility = 'visible';
                menu.style.transform = 'translateY(0)';
            });
            dropdown.addEventListener('mouseleave', () => {
                menu.style.opacity = '0';
                menu.style.visibility = 'hidden';
                menu.style.transform = 'translateY(10px)';
            });
        }
        dropdown.addEventListener('click', (e) => {
            if (window.innerWidth <= 768) {
                e.preventDefault();
                menu.style.display = menu.style.display === 'block' ? 'none' : 'block';
            }
        });
    });



    // --- Animate on Scroll ---
    const observerOptions = { root: null, rootMargin: '0px', threshold: 0.1 };
    const observer = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);
    document.querySelectorAll('.section-title, .section-subtitle, .process-step, .policy-card, .boat-card').forEach(element => {
        element.classList.add('hidden');
        observer.observe(element);
    });

    // --- Learn More Button Scroll ---
    var btn = document.querySelector('.btn-explore[href="#how-we-work"]');
    var section = document.getElementById('how-we-work');
    if (btn && section) {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            section.scrollIntoView({behavior: 'smooth', block: 'start'});
        });
    }

    // --- Search Overlay Functionality ---
    if (typeof initSearchFunctionality === 'function') {
        initSearchFunctionality();
    }

    // --- Page Load Animations ---
    if (typeof initPageLoadAnimations === 'function') {
        initPageLoadAnimations();
    }

    // --- Interactive Features ---
    if (typeof initInteractiveFeatures === 'function') {
        initInteractiveFeatures();
    }

    // --- Parallax Effects ---
    if (typeof initParallaxEffects === 'function') {
        initParallaxEffects();
    }

    // --- Destination Cards ---
    if (typeof initDestinationCards === 'function') {
        initDestinationCards();
    }

    // --- Smooth Scrolling ---
    if (typeof initSmoothScrolling === 'function') {
        initSmoothScrolling();
    }

    // --- Header Transparency (again for safety) ---
    if (typeof initHeaderTransparency === 'function') {
        initHeaderTransparency();
    }

    // Mobile Menu Toggle
    const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
    const navMenu = document.querySelector('.nav-menu');

    if (mobileMenuToggle && navMenu) {
        mobileMenuToggle.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            this.classList.toggle('active');
            navMenu.classList.toggle('active');
        });
    }

    // Close mobile menu when clicking outside
    document.addEventListener('click', function(event) {
        if (navMenu && navMenu.classList.contains('active') && !event.target.closest('.nav-container')) {
            mobileMenuToggle.classList.remove('active');
            navMenu.classList.remove('active');
        }
    });

    // Close mobile menu when clicking a link
    const menuLinks = document.querySelectorAll('.nav-menu .nav-link');
    menuLinks.forEach(link => {
        link.addEventListener('click', function() {
            if (navMenu.classList.contains('active')) {
                mobileMenuToggle.classList.remove('active');
                navMenu.classList.remove('active');
            }
        });
    });
});

