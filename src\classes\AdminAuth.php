<?php
class AdminAuth {
    private $db;
    private $security;

    public function __construct($pdo) {
        $this->db = Database::getInstance($pdo);
        $this->security = new Security($pdo);
    }

    public function login($username, $password) {
        try {
            // Check if IP is blocked
            $clientIP = filter_var($_SERVER['REMOTE_ADDR'], FILTER_VALIDATE_IP);
            if ($this->security->isIPBlocked($clientIP)) {
                throw new Exception("Your IP address has been blocked due to multiple failed attempts. Please try again later.");
            }

            // Check rate limiting
            if (!$this->security->checkRateLimit($clientIP)) {
                throw new Exception("Too many attempts. Please try again later.");
            }

            // Get admin user from admins table by username
            $admin = $this->db->queryOne(
                "SELECT admin_id, username, password, first_name, last_name, email, role FROM admins WHERE username = ?",
                [$username]
            );

            if (!$admin) {
                error_log("Admin user not found for username: " . $username);
                $this->security->logLoginAttempt($clientIP, $username, false);
                return false;
            }

            $passwordHash = $admin['password'];
            $passwordVerified = password_verify($password, $passwordHash);
            error_log("Password verification result: " . ($passwordVerified ? "true" : "false"));

            if (!$passwordVerified) {
                $this->security->logLoginAttempt($clientIP, $username, false);
                return false;
            }

            // Log successful login
            $this->security->logLoginAttempt($clientIP, $username, true);

            // Set session variables
            $_SESSION['admin_logged_in'] = true;
            $_SESSION['admin_id'] = $admin['admin_id'];
            $_SESSION['admin_username'] = $admin['username'];
            $_SESSION['admin_name'] = $admin['first_name'] . ' ' . $admin['last_name'];
            $_SESSION['last_activity'] = time();

            // Add for dashboard.php compatibility
            $_SESSION['aid'] = $admin['admin_id'];

            // Set user type based on role
            if ($admin['role'] === 'subadmin') {
                $_SESSION['utype'] = 0; // Sub-admin
            } else {
                $_SESSION['utype'] = 1; // Main admin
            }

            // Generate new CSRF token
            $_SESSION[CSRF_TOKEN_NAME] = $this->security->generateCSRFToken();

            return true;
        } catch (Exception $e) {
            error_log("Admin Login Error: " . $e->getMessage());
            throw $e;
        }
    }

    public function isLoggedIn() {
        if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
            return false;
        }

        // Check session timeout
        if (time() - $_SESSION['last_activity'] > LOGIN_TIMEOUT) {
            $this->logout();
            return false;
        }

        // Update last activity
        $_SESSION['last_activity'] = time();
        return true;
    }

    public function logout() {
        // Clear all session variables
        $_SESSION = array();

        // Destroy the session cookie
        if (isset($_COOKIE[session_name()])) {
            setcookie(session_name(), '', time() - 3600, '/');
        }

        // Destroy the session
        session_destroy();
    }

    public function requireLogin() {
        if (!$this->isLoggedIn()) {
            header('Location: /Online Booking Reservation System/php/pages/loginadmin/admin-login.php');
            exit;
        }
    }

    public function validateCSRF($token) {
        if (!isset($_SESSION[CSRF_TOKEN_NAME]) || $token !== $_SESSION[CSRF_TOKEN_NAME]) {
            throw new Exception("Invalid CSRF token");
        }
        return true;
    }

    public function getCurrentAdmin() {
        if (!$this->isLoggedIn()) {
            return null;
        }

        return $this->db->queryOne(
            "SELECT admin_id, first_name, last_name, email, role FROM admins WHERE admin_id = ?",
            [$_SESSION['admin_id']]
        );
    }

    public function updatePassword($adminId, $currentPassword, $newPassword) {
        $admin = $this->db->queryOne(
            "SELECT password FROM admins WHERE admin_id = ?",
            [$adminId]
        );

        if (!$admin || !password_verify($currentPassword, $admin['password'])) {
            throw new Exception("Current password is incorrect");
        }

        $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);

        return $this->db->update(
            'admins',
            ['password' => $hashedPassword],
            'admin_id = ?',
            [$adminId]
        );
    }
}
?>