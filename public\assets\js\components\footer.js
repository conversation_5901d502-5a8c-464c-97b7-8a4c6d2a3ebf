// Footer JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Newsletter form submission
    const newsletterForm = document.querySelector('.newsletter-form');
    if (newsletterForm) {
        newsletterForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const emailInput = this.querySelector('input[type="email"]');
            if (emailInput && emailInput.value) {
                // This is a placeholder - in a real implementation, you would send the email to a server-ramos 
                alert('Thank you for subscribing to our newsletter!');
                emailInput.value = '';
            } else {
                alert('Please enter a valid email address.');
            }
        });
    }

    // Social media links animation
    const socialLinks = document.querySelectorAll('.social-link');
    socialLinks.forEach(link => {
        // Make sure links are clickable
        link.style.pointerEvents = 'auto';

        // Add hover animations
        link.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
        });

        link.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });

        // Add click event to ensure links work
        link.addEventListener('click', function() {
            // Let the default link behavior happen
            // This is just to ensure the link is working
            console.log('Social link clicked:', this.getAttribute('href'));
        });
    });

    // Footer links animation
    const footerLinks = document.querySelectorAll('.footer-column ul li a');
    footerLinks.forEach(link => {
        link.addEventListener('mouseenter', function() {
            this.style.transform = 'translateX(5px)';
            this.style.color = '#ffd700';
        });

        link.addEventListener('mouseleave', function() {
            this.style.transform = 'translateX(0)';
            this.style.color = '#ccc';
        });
    });

    // Current year for copyright
    const copyrightYear = document.querySelector('.copyright p');
    if (copyrightYear) {
        const year = new Date().getFullYear();
        copyrightYear.innerHTML = copyrightYear.innerHTML.replace(/\d{4}/, year);
    }
});
