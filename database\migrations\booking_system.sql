-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: May 06, 2025 at 04:21 AM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.1.25

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";
SET FOREIGN_KEY_CHECKS = 0;
SET SQL_SAFE_UPDATES = 0;
SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION';

-- Important: This ensures all tables have proper AUTO_INCREMENT for IDs
-- and prevents issues with booking IDs in the admin dashboard

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `booking_system`
--

DELIMITER $$
--
-- Procedures
--
DROP PROCEDURE IF EXISTS `CheckBoatAvailabilityForDate` $$
CREATE DEFINER=CURRENT_USER PROCEDURE `CheckBoatAvailabilityForDate` (IN `p_date` DATE)   BEGIN
    SELECT
        b.boat_id,
        b.name,
        b.type,
        b.capacity,
        b.price_per_day,
        -- Check specific date availability first, then fall back to general status
        COALESCE(bad.status, b.availability_status) AS availability_status,
        CASE
            WHEN bad.status IS NOT NULL THEN 'Specific date status'
            ELSE 'General status'
        END AS status_source,
        bad.notes,
        -- Check if boat is already booked
        CASE
            WHEN bk.boat_id IS NOT NULL THEN 'Booked'
            ELSE 'Not Booked'
        END AS booking_status
    FROM
        boats b
    -- Check specific date availability
    LEFT JOIN
        boat_availability_dates bad ON b.boat_id = bad.boat_id AND bad.available_date = p_date
    -- Check if there are bookings for this date
    LEFT JOIN (
        SELECT DISTINCT boat_id
        FROM bookings
        WHERE DATE(start_date) = p_date
    ) bk ON b.boat_id = bk.boat_id
    ORDER BY
        b.name;
END$$

DROP PROCEDURE IF EXISTS `GenerateMonthlyTouristReport` $$
CREATE DEFINER=CURRENT_USER PROCEDURE `GenerateMonthlyTouristReport` (IN `report_month` INT, IN `report_year` INT, IN `admin_id` INT)   BEGIN
    DECLARE start_date DATE;
    DECLARE end_date DATE;
    DECLARE report_id_var INT;
    DECLARE most_visited VARCHAR(255);
    DECLARE total_tourists_var INT DEFAULT 0;
    DECLARE regular_tourists_var INT DEFAULT 0;
    DECLARE discounted_tourists_var INT DEFAULT 0;
    DECLARE children_tourists_var INT DEFAULT 0;
    DECLARE infants_tourists_var INT DEFAULT 0;
    DECLARE total_revenue_var DECIMAL(10,2) DEFAULT 0;
    DECLARE env_fee_var DECIMAL(10,2) DEFAULT 0;

    -- Set date range for the report
    SET start_date = CONCAT(report_year, '-', LPAD(report_month, 2, '0'), '-01');
    SET end_date = LAST_DAY(start_date);

    -- Find the most visited destination for this period
    SELECT d.name INTO most_visited
    FROM bookings b
    JOIN destinations d ON b.destination_id = d.destination_id
    WHERE b.start_date BETWEEN start_date AND end_date
      AND b.booking_status = 'confirmed'
    GROUP BY d.name
    ORDER BY COUNT(*) DESC
    LIMIT 1;

    -- Calculate totals
    SELECT
        SUM(no_of_pax) AS total_tourists,
        SUM(regular_pax) AS regular_tourists,
        SUM(discounted_pax) AS discounted_tourists,
        SUM(children_pax) AS children_tourists,
        SUM(infants_pax) AS infants_tourists,
        SUM(total) AS total_revenue,
        SUM(environmental_fee) AS env_fee
    INTO
        total_tourists_var,
        regular_tourists_var,
        discounted_tourists_var,
        children_tourists_var,
        infants_tourists_var,
        total_revenue_var,
        env_fee_var
    FROM bookings
    WHERE start_date BETWEEN start_date AND end_date
      AND booking_status = 'confirmed';

    -- Insert the report
    INSERT INTO tourist_reports (
        report_date,
        report_type,
        total_tourists,
        regular_tourists,
        discounted_tourists,
        children_tourists,
        infants_tourists,
        total_revenue,
        environmental_fee_collected,
        most_visited_destination,
        generated_by
    ) VALUES (
        end_date,
        'monthly',
        IFNULL(total_tourists_var, 0),
        IFNULL(regular_tourists_var, 0),
        IFNULL(discounted_tourists_var, 0),
        IFNULL(children_tourists_var, 0),
        IFNULL(infants_tourists_var, 0),
        IFNULL(total_revenue_var, 0),
        IFNULL(env_fee_var, 0),
        most_visited,
        admin_id
    );

    -- Get the inserted report ID
    SET report_id_var = LAST_INSERT_ID();

    -- Insert destination details
    INSERT INTO tourist_report_details (
        report_id,
        destination_id,
        destination_name,
        tourist_count,
        revenue
    )
    SELECT
        report_id_var,
        d.destination_id,
        d.name,
        SUM(b.no_of_pax),
        SUM(b.total)
    FROM bookings b
    JOIN destinations d ON b.destination_id = d.destination_id
    WHERE b.start_date BETWEEN start_date AND end_date
      AND b.booking_status = 'confirmed'
    GROUP BY d.destination_id, d.name;

    -- Insert origin statistics
    INSERT INTO tourist_origin_stats (
        report_id,
        origin_location,
        tourist_count,
        percentage
    )
    SELECT
        report_id_var,
        SUBSTRING_INDEX(address, ',', -2) AS origin,
        SUM(no_of_pax) AS count,
        (SUM(no_of_pax) / total_tourists_var) * 100 AS percentage
    FROM bookings
    WHERE start_date BETWEEN start_date AND end_date
      AND booking_status = 'confirmed'
    GROUP BY origin
    ORDER BY count DESC;

    -- Return the report ID
    SELECT report_id_var AS report_id;
END$$

DROP PROCEDURE IF EXISTS `GetAvailableBoats` $$
CREATE DEFINER=CURRENT_USER PROCEDURE `GetAvailableBoats` (IN `p_date` DATE)   BEGIN
    -- Get all boats
    SELECT
        b.boat_id,
        b.name,
        b.type,
        b.capacity,
        b.price_per_day,
        b.status,
        b.availability_status,
        -- Check if boat is already scheduled for the given date
        CASE WHEN bl.boat IS NULL THEN 'Available' ELSE 'Scheduled' END AS schedule_status
    FROM
        boats b
    LEFT JOIN (
        -- Get boats scheduled for the given date
        SELECT DISTINCT boat
        FROM booking_logs bl
        JOIN tourist t ON bl.user_id = t.user_id
        WHERE DATE(t.date_of_tour) = p_date
    ) bl ON b.name = bl.boat
    WHERE
        b.availability_status = 'available'
    ORDER BY
        b.name;
END$$

DROP PROCEDURE IF EXISTS `GetBoatSchedule` $$
CREATE DEFINER=CURRENT_USER PROCEDURE `GetBoatSchedule` (IN `p_start_date` DATE, IN `p_end_date` DATE)   BEGIN
    SELECT
        bl.log_id,
        bl.date_of_booking,
        bl.boat,
        t.full_name AS tourist_name,
        t.mobile_number,
        t.email_address,
        t.date_of_tour,
        t.number_of_pax,
        t.tour_destination,
        bl.price,
        a.username AS admin_name
    FROM
        booking_logs bl
    JOIN
        tourist t ON bl.user_id = t.user_id
    LEFT JOIN
        admins a ON bl.admin_id = a.admin_id
    WHERE
        t.date_of_tour BETWEEN p_start_date AND p_end_date
    ORDER BY
        t.date_of_tour, bl.boat;
END$$

DROP PROCEDURE IF EXISTS `MarkBoatAvailableForDate` $$
CREATE DEFINER=CURRENT_USER PROCEDURE `MarkBoatAvailableForDate` (IN `p_boat_id` INT, IN `p_date` DATE, IN `p_status` ENUM('available','not available','maintenance'), IN `p_notes` TEXT, IN `p_admin_id` INT)   BEGIN
    INSERT INTO boat_availability_dates (boat_id, available_date, status, notes, added_by)
    VALUES (p_boat_id, p_date, p_status, p_notes, p_admin_id)
    ON DUPLICATE KEY UPDATE
        status = p_status,
        notes = p_notes,
        added_by = p_admin_id,
        updated_at = CURRENT_TIMESTAMP();

    -- Log the action
    INSERT INTO activity_logs (admin_id, activity, activity_time)
    VALUES (p_admin_id, CONCAT('Updated boat ID ', p_boat_id, ' availability for ', p_date, ' to ', p_status), CURRENT_TIMESTAMP());
END$$

DROP PROCEDURE IF EXISTS `MarkBoatAvailableForDateRange` $$
CREATE DEFINER=CURRENT_USER PROCEDURE `MarkBoatAvailableForDateRange` (IN `p_boat_id` INT, IN `p_start_date` DATE, IN `p_end_date` DATE, IN `p_status` ENUM('available','not available','maintenance'), IN `p_notes` TEXT, IN `p_admin_id` INT)   BEGIN
    DECLARE v_current_date DATE;
    SET v_current_date = p_start_date;

    -- Loop through each date in the range
    WHILE v_current_date <= p_end_date DO
        -- Call the single date procedure
        CALL MarkBoatAvailableForDate(p_boat_id, v_current_date, p_status, p_notes, p_admin_id);

        -- Move to next day
        SET v_current_date = DATE_ADD(v_current_date, INTERVAL 1 DAY);
    END WHILE;

    -- Log the action
    INSERT INTO activity_logs (admin_id, activity, activity_time)
    VALUES (p_admin_id, CONCAT('Updated boat ID ', p_boat_id, ' availability for date range ', p_start_date, ' to ', p_end_date), CURRENT_TIMESTAMP());
END$$

DROP PROCEDURE IF EXISTS `PopulateBookingLogs` $$
CREATE DEFINER=CURRENT_USER PROCEDURE `PopulateBookingLogs` ()   BEGIN
    -- First ensure we have tourist records for all bookings
    INSERT IGNORE INTO `tourist` (`full_name`, `address`, `mobile_number`, `email_address`, `sex`, `birthdate`, `date_of_tour`, `number_of_pax`, `tour_destination`)
    SELECT
        CONCAT(b.first_name, ' ', b.last_name),
        b.address,
        b.contact_number,
        b.email,
        b.sex,
        CURDATE() - INTERVAL b.age YEAR, -- Approximate birthdate based on age
        DATE(b.start_date), -- Tour date from booking
        b.no_of_pax,
        b.tour_destination
    FROM
        bookings b
    WHERE
        b.email IS NOT NULL;

    -- Now populate booking_logs
    INSERT IGNORE INTO `booking_logs` (`user_id`, `date_of_booking`, `time`, `booking_id`, `boat`, `price`, `admin_id`)
    SELECT
        t.user_id,
        DATE(b.created_at),
        TIME(b.created_at),
        b.booking_id,
        bt.name,
        b.total,
        1 -- Default admin_id
    FROM
        bookings b
    JOIN
        boats bt ON b.boat_id = bt.boat_id
    JOIN
        tourist t ON t.email_address = b.email
    WHERE
        b.email IS NOT NULL;
END$$

DROP PROCEDURE IF EXISTS `ScheduleBoat` $$
CREATE DEFINER=CURRENT_USER PROCEDURE `ScheduleBoat` (IN `p_tourist_name` VARCHAR(100), IN `p_tourist_address` VARCHAR(255), IN `p_tourist_mobile` VARCHAR(20), IN `p_tourist_email` VARCHAR(100), IN `p_tourist_sex` ENUM('Male','Female','Other'), IN `p_tourist_birthdate` DATE, IN `p_tour_date` DATE, IN `p_number_of_pax` INT, IN `p_tour_destination` VARCHAR(255), IN `p_boat_name` VARCHAR(100), IN `p_price` DECIMAL(10,2), IN `p_admin_id` INT)   BEGIN
    DECLARE v_user_id INT;
    DECLARE v_log_id INT;

    -- Start transaction
    START TRANSACTION;

    -- Check if tourist already exists
    SELECT user_id INTO v_user_id FROM tourist
    WHERE email_address = p_tourist_email LIMIT 1;

    -- If tourist doesn't exist, create a new one
    IF v_user_id IS NULL THEN
        INSERT INTO tourist (
            full_name,
            address,
            mobile_number,
            email_address,
            sex,
            birthdate,
            date_of_tour,
            number_of_pax,
            tour_destination
        ) VALUES (
            p_tourist_name,
            p_tourist_address,
            p_tourist_mobile,
            p_tourist_email,
            p_tourist_sex,
            p_tourist_birthdate,
            p_tour_date,
            p_number_of_pax,
            p_tour_destination
        );

        SET v_user_id = LAST_INSERT_ID();
    ELSE
        -- Update existing tourist with new tour information
        UPDATE tourist SET
            date_of_tour = p_tour_date,
            number_of_pax = p_number_of_pax,
            tour_destination = p_tour_destination
        WHERE user_id = v_user_id;
    END IF;

    -- Create booking log entry
    INSERT INTO booking_logs (
        user_id,
        date_of_booking,
        time,
        booking_id,
        boat,
        price,
        admin_id
    ) VALUES (
        v_user_id,
        CURDATE(),
        CURTIME(),
        FLOOR(1000000 + RAND() * 9000000), -- Generate a random booking ID
        p_boat_name,
        p_price,
        p_admin_id
    );

    SET v_log_id = LAST_INSERT_ID();

    -- Commit transaction
    COMMIT;

    -- Return the created IDs
    SELECT v_user_id AS tourist_id, v_log_id AS booking_log_id;
END$$

DELIMITER ;

-- --------------------------------------------------------

--
-- Table structure for table `activity_logs`
--

CREATE TABLE `activity_logs` (
  `id` int(11) NOT NULL,
  `admin_id` int(11) NOT NULL,
  `activity` varchar(255) NOT NULL,
  `activity_time` datetime DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `activity_logs`
--

INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES
(1, 1, 'Uploaded profile image', '2025-05-01 05:46:00'),
(3, 3, 'Added new boat', '2025-05-01 05:46:00'),
(4, 1, 'Uploaded profile image', '2025-05-01 05:46:00'),
(6, 3, 'Added new boat', '2025-05-01 05:46:00'),
(7, 1, 'Uploaded profile image', '2025-05-01 05:46:00'),
(9, 3, 'Added new boat', '2025-05-01 05:46:00'),
(10, 1, 'Uploaded profile image', '2025-05-01 05:46:00'),
(12, 3, 'Added new boat', '2025-05-01 05:46:00'),
(13, 1, 'Uploaded profile image', '2025-05-01 05:46:00'),
(15, 3, 'Added new boat', '2025-05-01 05:46:00'),
(16, 1, 'Uploaded profile image', '2025-05-01 05:46:00'),
(18, 3, 'Added new boat', '2025-05-01 05:46:00'),
(19, 1, 'Uploaded profile image', '2025-05-01 05:46:00'),
(21, 3, 'Added new boat', '2025-05-01 05:46:00'),
(22, 1, 'Uploaded profile image', '2025-05-01 05:46:00'),
(24, 3, 'Added new boat', '2025-05-01 05:46:00'),
(25, 1, 'Uploaded profile image', '2025-05-01 05:46:00'),
(27, 3, 'Added new boat', '2025-05-01 05:46:00'),
(28, 1, 'Uploaded profile image', '2025-05-01 05:46:00'),
(30, 3, 'Added new boat', '2025-05-01 05:46:00'),
(31, 1, 'Uploaded profile image', '2025-05-01 05:46:00'),
(33, 3, 'Added new boat', '2025-05-01 05:46:00'),
(34, 1, 'Uploaded profile image', '2025-05-01 05:46:00'),
(36, 3, 'Added new boat', '2025-05-01 05:46:00'),
(37, 1, 'Uploaded profile image', '2025-05-01 05:46:00'),
(39, 3, 'Added new boat', '2025-05-01 05:46:00'),
(40, 1, 'Uploaded profile image', '2025-05-01 05:46:00'),
(42, 3, 'Added new boat', '2025-05-01 05:46:00');

-- --------------------------------------------------------

--
-- Table structure for table `admins`
--

CREATE TABLE `admins` (
  `admin_id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `first_name` varchar(100) NOT NULL,
  `last_name` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `role` varchar(50) NOT NULL,
  `status` varchar(20) NOT NULL,
  `last_login` datetime DEFAULT NULL,
  `last_activity` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`admin_id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `admins`
--

INSERT IGNORE INTO `admins` (`admin_id`, `username`, `password`, `first_name`, `last_name`, `email`, `phone`, `role`, `status`, `last_login`, `last_activity`, `created_at`, `updated_at`) VALUES
(1, 'admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Admin', 'Admin', '<EMAIL>', '09123456789', 'super_admin', 'active', '2025-04-28 17:28:04', '2025-05-06 10:20:26', '2025-04-14 14:37:35', '2025-04-28 17:28:04'),
(2, 'jhona', '$2y$10$abcdefghijABCDEFGHIJklmnopqrstuvwx/yz1234567890', 'Jhona Mae', 'Santander', '<EMAIL>', '09173456789', 'sub_admin', 'active', '2025-04-27 09:00:00', NULL, '2025-04-01 11:00:00', '2025-04-27 09:00:00'),
(4, 'jordan', '$2y$10$klmnopqrstuvwxABCDEFGHIJabcdefghij/yz1234567890', 'Jordan', 'Barcarlos', '<EMAIL>', '09183456789', 'sub_admin', 'active', '2025-04-25 14:00:00', NULL, '2025-04-03 13:00:00', '2025-04-25 14:00:00'),
(6, 'lhance', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Lhance', 'Montero', '<EMAIL>', '09123456789', 'sub_admin', 'active', '2025-04-28 18:00:00', NULL, '2025-04-14 15:00:00', '2025-04-28 18:00:00');

-- --------------------------------------------------------

--
-- Table structure for table `boats`
--

CREATE TABLE `boats` (
  `boat_id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `type` varchar(50) DEFAULT 'small',
  `capacity` int(11) DEFAULT 10,
  `price_per_day` decimal(10,2) NOT NULL,
  `description` text DEFAULT NULL,
  `status` varchar(20) DEFAULT 'Available',
  `destination` varchar(255) DEFAULT NULL,
  `availability_status` enum('available','not available','maintenance') DEFAULT 'available',
  `created_at` datetime DEFAULT current_timestamp(),
  PRIMARY KEY (`boat_id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `boats`
--

INSERT INTO `boats` (`boat_id`, `name`, `type`, `capacity`, `price_per_day`, `description`, `status`, `destination`, `availability_status`, `created_at`) VALUES
(1, 'Boat 1', 'small', 10, 2000.00, 'Description for Boat 1', 'Available', 'Carles', 'available', '2025-05-05 20:47:15'),
(2, 'Boat 2', 'medium', 15, 3500.00, 'Description for Boat 2', 'Available', 'Carles', 'available', '2025-05-05 20:47:15'),
(3, 'Boat 3', 'large', 20, 3500.00, 'Description for Boat 3', 'Available', 'Carles', 'available', '2025-05-05 20:47:15'),
(4, 'Boat 4', 'special', 10, 3500.00, 'Description for Boat 4', 'Available', 'Carles', 'available', '2025-05-05 20:47:15');

-- --------------------------------------------------------

--
-- Table structure for table `boat_availability_dates`
--

CREATE TABLE `boat_availability_dates` (
  `id` int(11) NOT NULL,
  `boat_id` int(11) NOT NULL,
  `available_date` date NOT NULL,
  `status` enum('available','not available','maintenance') NOT NULL DEFAULT 'available',
  `notes` text DEFAULT NULL,
  `added_by` int(11) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `boat_availability_view`
--

CREATE TABLE `boat_availability_view` (
  `boat_id` int(11) DEFAULT NULL,
  `boat_name` varchar(255) DEFAULT NULL,
  `type` varchar(50) DEFAULT NULL,
  `capacity` int(11) DEFAULT NULL,
  `price_per_day` decimal(10,2) DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  `availability_status` enum('available','not available','maintenance') DEFAULT NULL,
  `scheduled_dates` longtext DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `boat_reservations`
--

CREATE TABLE `boat_reservations` (
  `booking_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `boat_id` int(11) NOT NULL,
  `booking_code` varchar(50) NOT NULL,
  `booking_date` date NOT NULL,
  `start_time` time NOT NULL,
  `booking_status` enum('pending','confirmed','cancelled') NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `boat_reservations`
--

INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES
(1, 1, 1, 'BOAT1-20250109-68532', '2025-01-20', '09:28:16', 'pending', '2025-01-19 01:28:16'),
(2, 2, 2, 'BOAT2-20250114-12345', '2025-02-02', '10:00:00', 'confirmed', '2025-02-01 02:00:00'),
(3, 3, 3, 'BOAT3-20250209-67890', '2025-02-10', '13:00:00', 'cancelled', '2025-02-09 05:00:00'),
(4, 4, 2, 'BOAT2-20250224-88888', '2025-03-05', '08:00:00', 'pending', '2025-03-04 07:00:00'),
(5, 5, 3, 'BOAT3-20250303-77777', '2025-03-18', '11:00:00', 'confirmed', '2025-03-17 08:00:00'),
(6, 6, 1, 'BOAT1-20250311-68532', '2025-03-29', '07:30:00', 'pending', '2025-03-28 06:00:00'),
(7, 7, 2, 'BOAT2-20250314-12345', '2025-04-10', '10:30:00', 'cancelled', '2025-04-09 09:00:00'),
(8, 8, 1, 'BOAT1-20250406-77777', '2025-04-15', '09:00:00', 'confirmed', '2025-04-14 08:00:00'),
(9, 9, 3, 'BOAT3-20250403-67890', '2025-01-25', '08:15:00', 'pending', '2025-01-24 07:00:00'),
(10, 10, 2, 'BOAT2-20250404-88888', '2025-02-12', '09:45:00', 'confirmed', '2025-02-11 08:00:00'),
(11, 11, 1, 'BOAT1-20250408-68532', '2025-03-03', '10:30:00', 'cancelled', '2025-03-02 09:00:00'),
(12, 12, 2, 'BOAT2-20250409-12345', '2025-03-22', '14:00:00', 'confirmed', '2025-03-21 12:00:00'),
(13, 13, 1, 'BOAT1-20250410-88888', '2025-04-05', '07:45:00', 'pending', '2025-04-04 06:00:00'),
(14, 14, 2, 'BOAT2-20250114-12345', '2025-01-28', '08:45:00', 'pending', '2025-01-27 07:30:00'),
(15, 15, 3, 'BOAT3-20250209-67890', '2025-02-18', '10:20:00', 'pending', '2025-02-17 08:20:00'),
(16, 16, 1, 'BOAT1-20250311-68532', '2025-03-12', '09:10:00', 'pending', '2025-03-11 07:10:00'),
(17, 17, 2, 'BOAT2-20250314-12345', '2025-04-08', '11:40:00', 'pending', '2025-04-07 09:40:00'),
(18, 1, 1, 'BOAT1-20250406-77777', '2025-04-18', '07:55:00', 'pending', '2025-04-17 06:55:00'),
(19, 1, 1, 'BOAT1-20250109-68532', '2025-01-20', '09:28:16', 'pending', '2025-01-19 01:28:16'),
(20, 2, 2, 'BOAT2-20250114-12345', '2025-02-02', '10:00:00', 'confirmed', '2025-02-01 02:00:00'),
(21, 3, 3, 'BOAT3-20250209-67890', '2025-02-10', '13:00:00', 'cancelled', '2025-02-09 05:00:00'),
(22, 4, 2, 'BOAT2-20250224-88888', '2025-03-05', '08:00:00', 'pending', '2025-03-04 07:00:00'),
(23, 5, 3, 'BOAT3-20250303-77777', '2025-03-18', '11:00:00', 'confirmed', '2025-03-17 08:00:00'),
(24, 6, 1, 'BOAT1-20250311-68532', '2025-03-29', '07:30:00', 'pending', '2025-03-28 06:00:00'),
(25, 7, 2, 'BOAT2-20250314-12345', '2025-04-10', '10:30:00', 'cancelled', '2025-04-09 09:00:00'),
(26, 8, 1, 'BOAT1-20250406-77777', '2025-04-15', '09:00:00', 'confirmed', '2025-04-14 08:00:00'),
(27, 9, 3, 'BOAT3-20250403-67890', '2025-01-25', '08:15:00', 'pending', '2025-01-24 07:00:00'),
(28, 10, 2, 'BOAT2-20250404-88888', '2025-02-12', '09:45:00', 'confirmed', '2025-02-11 08:00:00'),
(29, 11, 1, 'BOAT1-20250408-68532', '2025-03-03', '10:30:00', 'cancelled', '2025-03-02 09:00:00'),
(30, 12, 2, 'BOAT2-20250409-12345', '2025-03-22', '14:00:00', 'confirmed', '2025-03-21 12:00:00'),
(31, 13, 1, 'BOAT1-20250410-88888', '2025-04-05', '07:45:00', 'pending', '2025-04-04 06:00:00'),
(32, 14, 2, 'BOAT2-20250114-12345', '2025-01-28', '08:45:00', 'pending', '2025-01-27 07:30:00'),
(33, 15, 3, 'BOAT3-20250209-67890', '2025-02-18', '10:20:00', 'pending', '2025-02-17 08:20:00'),
(34, 16, 1, 'BOAT1-20250311-68532', '2025-03-12', '09:10:00', 'pending', '2025-03-11 07:10:00'),
(35, 17, 2, 'BOAT2-20250314-12345', '2025-04-08', '11:40:00', 'pending', '2025-04-07 09:40:00'),
(36, 1, 1, 'BOAT1-20250406-77777', '2025-04-18', '07:55:00', 'pending', '2025-04-17 06:55:00'),
(37, 1, 1, 'BOAT1-20250109-68532', '2025-01-20', '09:28:16', 'pending', '2025-01-19 01:28:16'),
(38, 2, 2, 'BOAT2-20250114-12345', '2025-02-02', '10:00:00', 'confirmed', '2025-02-01 02:00:00'),
(39, 3, 3, 'BOAT3-20250209-67890', '2025-02-10', '13:00:00', 'cancelled', '2025-02-09 05:00:00'),
(40, 4, 2, 'BOAT2-20250224-88888', '2025-03-05', '08:00:00', 'pending', '2025-03-04 07:00:00'),
(41, 5, 3, 'BOAT3-20250303-77777', '2025-03-18', '11:00:00', 'confirmed', '2025-03-17 08:00:00'),
(42, 6, 1, 'BOAT1-20250311-68532', '2025-03-29', '07:30:00', 'pending', '2025-03-28 06:00:00'),
(43, 7, 2, 'BOAT2-20250314-12345', '2025-04-10', '10:30:00', 'cancelled', '2025-04-09 09:00:00'),
(44, 8, 1, 'BOAT1-20250406-77777', '2025-04-15', '09:00:00', 'confirmed', '2025-04-14 08:00:00'),
(45, 9, 3, 'BOAT3-20250403-67890', '2025-01-25', '08:15:00', 'pending', '2025-01-24 07:00:00'),
(46, 10, 2, 'BOAT2-20250404-88888', '2025-02-12', '09:45:00', 'confirmed', '2025-02-11 08:00:00'),
(47, 11, 1, 'BOAT1-20250408-68532', '2025-03-03', '10:30:00', 'cancelled', '2025-03-02 09:00:00'),
(48, 12, 2, 'BOAT2-20250409-12345', '2025-03-22', '14:00:00', 'confirmed', '2025-03-21 12:00:00'),
(49, 13, 1, 'BOAT1-20250410-88888', '2025-04-05', '07:45:00', 'pending', '2025-04-04 06:00:00'),
(50, 14, 2, 'BOAT2-20250114-12345', '2025-01-28', '08:45:00', 'pending', '2025-01-27 07:30:00'),
(51, 15, 3, 'BOAT3-20250209-67890', '2025-02-18', '10:20:00', 'pending', '2025-02-17 08:20:00'),
(52, 16, 1, 'BOAT1-20250311-68532', '2025-03-12', '09:10:00', 'pending', '2025-03-11 07:10:00'),
(53, 17, 2, 'BOAT2-20250314-12345', '2025-04-08', '11:40:00', 'pending', '2025-04-07 09:40:00'),
(54, 1, 1, 'BOAT1-20250406-77777', '2025-04-18', '07:55:00', 'pending', '2025-04-17 06:55:00'),
(55, 1, 1, 'BOAT1-20250109-68532', '2025-01-20', '09:28:16', 'pending', '2025-01-19 01:28:16'),
(56, 2, 2, 'BOAT2-20250114-12345', '2025-02-02', '10:00:00', 'confirmed', '2025-02-01 02:00:00'),
(57, 3, 3, 'BOAT3-20250209-67890', '2025-02-10', '13:00:00', 'cancelled', '2025-02-09 05:00:00'),
(58, 4, 2, 'BOAT2-20250224-88888', '2025-03-05', '08:00:00', 'pending', '2025-03-04 07:00:00'),
(59, 5, 3, 'BOAT3-20250303-77777', '2025-03-18', '11:00:00', 'confirmed', '2025-03-17 08:00:00'),
(60, 6, 1, 'BOAT1-20250311-68532', '2025-03-29', '07:30:00', 'pending', '2025-03-28 06:00:00'),
(61, 7, 2, 'BOAT2-20250314-12345', '2025-04-10', '10:30:00', 'cancelled', '2025-04-09 09:00:00'),
(62, 8, 1, 'BOAT1-20250406-77777', '2025-04-15', '09:00:00', 'confirmed', '2025-04-14 08:00:00'),
(63, 9, 3, 'BOAT3-20250403-67890', '2025-01-25', '08:15:00', 'pending', '2025-01-24 07:00:00'),
(64, 10, 2, 'BOAT2-20250404-88888', '2025-02-12', '09:45:00', 'confirmed', '2025-02-11 08:00:00'),
(65, 11, 1, 'BOAT1-20250408-68532', '2025-03-03', '10:30:00', 'cancelled', '2025-03-02 09:00:00'),
(66, 12, 2, 'BOAT2-20250409-12345', '2025-03-22', '14:00:00', 'confirmed', '2025-03-21 12:00:00'),
(67, 13, 1, 'BOAT1-20250410-88888', '2025-04-05', '07:45:00', 'pending', '2025-04-04 06:00:00'),
(68, 14, 2, 'BOAT2-20250114-12345', '2025-01-28', '08:45:00', 'pending', '2025-01-27 07:30:00'),
(69, 15, 3, 'BOAT3-20250209-67890', '2025-02-18', '10:20:00', 'pending', '2025-02-17 08:20:00'),
(70, 16, 1, 'BOAT1-20250311-68532', '2025-03-12', '09:10:00', 'pending', '2025-03-11 07:10:00'),
(71, 17, 2, 'BOAT2-20250314-12345', '2025-04-08', '11:40:00', 'pending', '2025-04-07 09:40:00'),
(72, 1, 1, 'BOAT1-20250406-77777', '2025-04-18', '07:55:00', 'pending', '2025-04-17 06:55:00'),
(73, 1, 1, 'BOAT1-20250109-68532', '2025-01-20', '09:28:16', 'pending', '2025-01-19 01:28:16'),
(74, 2, 2, 'BOAT2-20250114-12345', '2025-02-02', '10:00:00', 'confirmed', '2025-02-01 02:00:00'),
(75, 3, 3, 'BOAT3-20250209-67890', '2025-02-10', '13:00:00', 'cancelled', '2025-02-09 05:00:00'),
(76, 4, 2, 'BOAT2-20250224-88888', '2025-03-05', '08:00:00', 'pending', '2025-03-04 07:00:00'),
(77, 5, 3, 'BOAT3-20250303-77777', '2025-03-18', '11:00:00', 'confirmed', '2025-03-17 08:00:00'),
(78, 6, 1, 'BOAT1-20250311-68532', '2025-03-29', '07:30:00', 'pending', '2025-03-28 06:00:00'),
(79, 7, 2, 'BOAT2-20250314-12345', '2025-04-10', '10:30:00', 'cancelled', '2025-04-09 09:00:00'),
(80, 8, 1, 'BOAT1-20250406-77777', '2025-04-15', '09:00:00', 'confirmed', '2025-04-14 08:00:00'),
(81, 9, 3, 'BOAT3-20250403-67890', '2025-01-25', '08:15:00', 'pending', '2025-01-24 07:00:00'),
(82, 10, 2, 'BOAT2-20250404-88888', '2025-02-12', '09:45:00', 'confirmed', '2025-02-11 08:00:00'),
(83, 11, 1, 'BOAT1-20250408-68532', '2025-03-03', '10:30:00', 'cancelled', '2025-03-02 09:00:00'),
(84, 12, 2, 'BOAT2-20250409-12345', '2025-03-22', '14:00:00', 'confirmed', '2025-03-21 12:00:00'),
(85, 13, 1, 'BOAT1-20250410-88888', '2025-04-05', '07:45:00', 'pending', '2025-04-04 06:00:00'),
(86, 14, 2, 'BOAT2-20250114-12345', '2025-01-28', '08:45:00', 'pending', '2025-01-27 07:30:00'),
(87, 15, 3, 'BOAT3-20250209-67890', '2025-02-18', '10:20:00', 'pending', '2025-02-17 08:20:00'),
(88, 16, 1, 'BOAT1-20250311-68532', '2025-03-12', '09:10:00', 'pending', '2025-03-11 07:10:00'),
(89, 17, 2, 'BOAT2-20250314-12345', '2025-04-08', '11:40:00', 'pending', '2025-04-07 09:40:00'),
(90, 1, 1, 'BOAT1-20250406-77777', '2025-04-18', '07:55:00', 'pending', '2025-04-17 06:55:00'),
(91, 1, 1, 'BOAT1-20250109-68532', '2025-01-20', '09:28:16', 'pending', '2025-01-19 01:28:16'),
(92, 2, 2, 'BOAT2-20250114-12345', '2025-02-02', '10:00:00', 'confirmed', '2025-02-01 02:00:00'),
(93, 3, 3, 'BOAT3-20250209-67890', '2025-02-10', '13:00:00', 'cancelled', '2025-02-09 05:00:00'),
(94, 4, 2, 'BOAT2-20250224-88888', '2025-03-05', '08:00:00', 'pending', '2025-03-04 07:00:00'),
(95, 5, 3, 'BOAT3-20250303-77777', '2025-03-18', '11:00:00', 'confirmed', '2025-03-17 08:00:00');

-- --------------------------------------------------------

--
-- Table structure for table `bookings`
--

CREATE TABLE `bookings` (
  `booking_id` int(11) NOT NULL AUTO_INCREMENT,
  `customer_id` int(11) DEFAULT NULL,
  `first_name` varchar(255) DEFAULT NULL,
  `last_name` varchar(255) DEFAULT NULL,
  `age` int(11) DEFAULT NULL,
  `sex` varchar(10) DEFAULT NULL,
  `contact_number` varchar(20) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `address` varchar(255) DEFAULT NULL,
  `emergency_name` varchar(255) DEFAULT NULL,
  `emergency_number` varchar(20) DEFAULT NULL,
  `boat_id` int(11) NOT NULL,
  `no_of_pax` int(11) NOT NULL,
  `regular_pax` int(11) NOT NULL DEFAULT 0,
  `discounted_pax` int(11) NOT NULL DEFAULT 0,
  `children_pax` int(11) NOT NULL DEFAULT 0,
  `infants_pax` int(11) NOT NULL DEFAULT 0,
  `start_date` datetime DEFAULT NULL,
  `end_date` datetime DEFAULT NULL,
  `booking_time` datetime NOT NULL,
  `environmental_fee` decimal(10,2) NOT NULL,
  `payment_method` varchar(50) NOT NULL,
  `total` decimal(10,2) NOT NULL,
  `booking_status` enum('pending','confirmed','cancelled','accepted','rejected','verification_pending') NOT NULL DEFAULT 'pending',
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `tour_destination` varchar(255) DEFAULT NULL,
  `drop_off_location` varchar(255) DEFAULT NULL,
  `booking_code` varchar(50) NOT NULL,
  `destination_id` int(11) NOT NULL,
  PRIMARY KEY (`booking_id`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `bookings`
--

INSERT INTO `bookings` (`booking_id`, `customer_id`, `first_name`, `last_name`, `age`, `sex`, `contact_number`, `email`, `address`, `emergency_name`, `emergency_number`, `boat_id`, `no_of_pax`, `regular_pax`, `discounted_pax`, `children_pax`, `infants_pax`, `start_date`, `end_date`, `booking_time`, `environmental_fee`, `payment_method`, `total`, `booking_status`, `created_at`, `tour_destination`, `drop_off_location`, `booking_code`, `destination_id`) VALUES
(1, 1, 'Ralph', 'Ramos', 21, 'male', '09345789658', '<EMAIL>', 'Carles, Iloilo', 'Jose Ramos', '09999000111', 1, 25, 20, 3, 1, 1, '2025-05-01 10:20:26', '2025-05-02 10:20:26', '2025-04-29 10:20:26', 1000.00, 'gcash', 5000.00, 'confirmed', '2025-04-28 10:20:26', 'Tumaquin Island', 'Carles Tourism Office', 'BOAT1-20250109-68532', 1),
(2, 2, 'Maria', 'Santos', 25, 'female', '09181234567', '<EMAIL>', 'Carles, Iloilo', 'Miguel Santos', '09000111222', 2, 4, 2, 1, 1, 0, '2025-05-01 10:20:26', '2025-05-02 10:20:26', '2025-04-29 10:20:26', 160.00, 'manual', 3500.00, 'confirmed', '2025-04-28 10:20:26', 'Gigantes Island', 'Estancia Port', 'BOAT2-20250114-12345', 2),
(3, 3, 'John', 'Doe', 30, 'male', '09175553333', '<EMAIL>', 'Balasan, Iloilo', 'Jane Doe', '09111222333', 3, 8, 5, 1, 0, 2, '2025-05-05 10:20:26', '2025-05-07 10:20:26', '2025-05-04 10:20:26', 3200.00, 'gcash', 2000.00, 'pending', '2025-05-03 10:20:26', 'Sicogon Island', 'Balasan Port', 'BOAT3-20250209-67890', 4),
(4, 2, 'Maria', 'Santos', 25, 'female', '09181234567', '<EMAIL>', 'Carles, Iloilo', 'Pedro Santos', '09222333444', 2, 4, 2, 0, 0, 2, '2025-05-01 10:20:26', '2025-05-02 10:20:26', '2025-04-29 10:20:26', 2000.00, 'gcash', 1080.00, 'confirmed', '2025-04-28 10:20:26', 'Bayas Island', 'Carles Main Port', 'BOAT2-20250224-88888', 3),
(5, 3, 'John', 'Doe', 30, 'male', '09175553333', '<EMAIL>', 'Balasan, Iloilo', 'Mary Doe', '09333444555', 1, 3, 2, 0, 0, 1, '2025-05-05 10:20:26', '2025-05-07 10:20:26', '2025-05-04 10:20:26', 1500.00, 'manual', 1080.00, 'pending', '2025-05-03 10:20:26', 'Cabugao Gamay', 'Balasan Pier', 'BOAT3-20250303-77777', 1),
(6, 1, 'Jhona Mae', 'Santander', 21, 'female', '09345797658', '<EMAIL>', 'Sicogon Island,Carles, Iloilo', 'Rico Santander', '09444555666', 1, 25, 17, 3, 2, 3, '2025-05-01 10:20:26', '2025-05-02 10:20:26', '2025-04-29 10:20:26', 1815.00, 'manual', 815.00, 'confirmed', '2025-04-28 10:20:26', 'Antonia Beach', 'Sicogon Port', 'BOAT1-20250311-68532', 3),
(7, 2, 'Maria', 'Santos', 25, 'female', '09181234567', '<EMAIL>', 'Carles, Iloilo', 'Carlos Santos', '09555666777', 2, 4, 2, 0, 0, 2, '2025-05-05 10:20:26', '2025-05-07 10:20:26', '2025-05-04 10:20:26', 2000.00, 'gcash', 4200.00, 'pending', '2025-05-03 10:20:26', 'Tangke Lagoon', 'Estancia Main Port', 'BOAT2-20250314-12345', 4),
(8, 1, 'Ralph', 'Ramos', 21, 'male', '09495334604', '<EMAIL>', 'Carles, Iloilo', 'Liza Ramos', '09666777888', 2, 4, 2, 0, 0, 2, '2025-05-01 10:20:26', '2025-05-02 10:20:26', '2025-04-29 10:20:26', 2000.00, 'manual', 1590.00, 'confirmed', '2025-04-28 10:20:26', 'Agho Island', 'Carles Tourism Port', 'BOAT1-20250406-77777', 2),
(9, 2, 'Maria', 'Santos', 25, 'female', '09181234567', '<EMAIL>', 'Carles, Iloilo', 'Ana Santos', '09777888999', 3, 8, 5, 1, 0, 2, '2025-05-05 10:20:26', '2025-05-07 10:20:26', '2025-05-04 10:20:26', 3200.00, 'manual', 1200.00, 'pending', '2025-05-03 10:20:26', 'Bantigue Sandbar', 'Carles Port Area', 'BOAT3-20250403-67890', 1),
(10, 3, 'John', 'Doe', 30, 'male', '09175553333', '<EMAIL>', 'Balasan, Iloilo', 'Sarah Doe', '09888999000', 1, 3, 2, 0, 0, 1, '2025-04-26 10:20:26', '2025-04-27 10:20:26', '2025-04-24 10:20:26', 1500.00, 'gcash', 6000.00, 'cancelled', '2025-04-23 10:20:26', 'Pan de Azucar', 'Balasan Main Port', 'BOAT2-20250404-88888', 3),
(11, 1, 'Ralph', 'Ramos', 21, 'male', '0934579658', '<EMAIL>', 'Carles, Iloilo', 'Jose Ramos', '09999000111', 3, 8, 5, 1, 0, 2, '2025-05-01 10:20:26', '2025-05-02 10:20:26', '2025-04-29 10:20:26', 3200.00, 'manual', 1400.00, 'confirmed', '2025-04-28 10:20:26', 'Bucari Highlands', 'Carles Tourism Office', 'BOAT2-20250409-12345', 8),
(12, 2, 'Maria', 'Santos', 25, 'female', '09181234567', '<EMAIL>', 'Carles, Iloilo', 'Miguel Santos', '09000111222', 1, 25, 17, 3, 2, 3, '2025-05-05 10:20:26', '2025-05-07 10:20:26', '2025-05-04 10:20:26', 1815.00, 'manual', 1636.00, 'pending', '2025-05-03 10:20:26', 'San Joaquin Campo Santo', 'Estancia Port', 'BOAT2-20250114-12345', 3),
(14, 1, 'Ralph', 'Ramos', 21, 'male', '09345789658', '<EMAIL>', 'Carles, Iloilo', 'Maria Ramos', '09123456789', 1, 25, 17, 3, 2, 3, '2025-05-05 10:20:26', '2025-05-07 10:20:26', '2025-05-04 10:20:26', 1815.00, 'manual', 7815.00, 'pending', '2025-05-03 10:20:26', 'Tumaquin Island', 'Carles Port', 'BOAT1-20250109-68532', 2),
(15, 2, 'Maria', 'Santos', 25, 'female', '09181234567', '<EMAIL>', 'Carles, Iloilo', 'Juan Santos', '09987654321', 2, 4, 2, 0, 0, 2, '2025-05-01 10:20:26', '2025-05-02 10:20:26', '2025-04-29 10:20:26', 2000.00, 'gcash', 3000.00, 'confirmed', '2025-04-28 10:20:26', 'Gigantes Island', 'Estancia Port', 'BOAT2-20250114-12345', 6),
(16, 3, 'John', 'Doe', 30, 'male', '09175553333', '<EMAIL>', 'Balasan, Iloilo', 'Jane Doe', '09111222333', 3, 8, 5, 1, 0, 2, '2025-05-05 10:20:26', '2025-05-07 10:20:26', '2025-05-04 10:20:26', 3200.00, 'gcash', 2000.00, 'pending', '2025-05-03 10:20:26', 'Sicogon Island', 'Balasan Port', 'BOAT3-20250209-67890', 4),
(17, 2, 'Maria', 'Santos', 25, 'female', '09181234567', '<EMAIL>', 'Carles, Iloilo', 'Pedro Santos', '09222333444', 2, 4, 2, 0, 0, 2, '2025-05-01 10:20:26', '2025-05-02 10:20:26', '2025-04-29 10:20:26', 2000.00, 'gcash', 1080.00, 'confirmed', '2025-04-28 10:20:26', 'Bayas Island', 'Carles Main Port', 'BOAT2-20250224-88888', 3),
(18, 3, 'John', 'Doe', 30, 'male', '09175553333', '<EMAIL>', 'Balasan, Iloilo', 'Mary Doe', '09333444555', 1, 3, 2, 0, 0, 1, '2025-05-05 10:20:26', '2025-05-07 10:20:26', '2025-05-04 10:20:26', 1500.00, 'manual', 1080.00, 'pending', '2025-05-03 10:20:26', 'Cabugao Gamay', 'Balasan Pier', 'BOAT3-20250303-77777', 1),
(19, 1, 'Jhona Mae', 'Santander', 21, 'female', '09345797658', '<EMAIL>', 'Sicogon Island,Carles, Iloilo', 'Rico Santander', '09444555666', 1, 25, 17, 3, 2, 3, '2025-05-01 10:20:26', '2025-05-02 10:20:26', '2025-04-29 10:20:26', 1815.00, 'manual', 815.00, 'confirmed', '2025-04-28 10:20:26', 'Antonia Beach', 'Sicogon Port', 'BOAT1-20250311-68532', 3),
(20, 2, 'Maria', 'Santos', 25, 'female', '09181234567', '<EMAIL>', 'Carles, Iloilo', 'Carlos Santos', '09555666777', 2, 4, 2, 0, 0, 2, '2025-05-05 10:20:26', '2025-05-07 10:20:26', '2025-05-04 10:20:26', 2000.00, 'gcash', 4200.00, 'pending', '2025-05-03 10:20:26', 'Tangke Lagoon', 'Estancia Main Port', 'BOAT2-20250314-12345', 4),
(21, 1, 'Ralph', 'Ramos', 21, 'male', '09495334604', '<EMAIL>', 'Carles, Iloilo', 'Liza Ramos', '09666777888', 2, 4, 2, 0, 0, 2, '2025-05-01 10:20:26', '2025-05-02 10:20:26', '2025-04-29 10:20:26', 2000.00, 'manual', 1590.00, 'confirmed', '2025-04-28 10:20:26', 'Agho Island', 'Carles Tourism Port', 'BOAT1-20250406-77777', 2),
(22, 2, 'Maria', 'Santos', 25, 'female', '09181234567', '<EMAIL>', 'Carles, Iloilo', 'Ana Santos', '09777888999', 3, 8, 5, 1, 0, 2, '2025-05-05 10:20:26', '2025-05-07 10:20:26', '2025-05-04 10:20:26', 3200.00, 'manual', 1200.00, 'pending', '2025-05-03 10:20:26', 'Bantigue Sandbar', 'Carles Port Area', 'BOAT3-20250403-67890', 1),
(23, 3, 'John', 'Doe', 30, 'male', '09175553333', '<EMAIL>', 'Balasan, Iloilo', 'Sarah Doe', '09888999000', 1, 3, 2, 0, 0, 1, '2025-04-26 10:20:26', '2025-04-27 10:20:26', '2025-04-24 10:20:26', 1500.00, 'gcash', 6000.00, 'cancelled', '2025-04-23 10:20:26', 'Pan de Azucar', 'Balasan Main Port', 'BOAT2-20250404-88888', 3),
(24, 1, 'Ralph', 'Ramos', 21, 'male', '0934579658', '<EMAIL>', 'Carles, Iloilo', 'Jose Ramos', '09999000111', 3, 8, 5, 1, 0, 2, '2025-05-01 10:20:26', '2025-05-02 10:20:26', '2025-04-29 10:20:26', 3200.00, 'manual', 1400.00, 'confirmed', '2025-04-28 10:20:26', 'Bucari Highlands', 'Carles Tourism Office', 'BOAT2-20250409-12345', 1),
(25, 2, 'Maria', 'Santos', 25, 'female', '09181234567', '<EMAIL>', 'Carles, Iloilo', 'Miguel Santos', '09000111222', 1, 25, 17, 3, 2, 3, '2025-05-05 10:20:26', '2025-05-07 10:20:26', '2025-05-04 10:20:26', 1815.00, 'manual', 1636.00, 'pending', '2025-05-03 10:20:26', 'San Joaquin Campo Santo', 'Estancia Port', 'BOAT2-20250114-12345', 3),
(26, NULL, 'Test', 'User', 30, 'Male', '09123456789', '<EMAIL>', 'Test Address', 'Emergency Contact', '09876543210', 1, 2, 0, 0, 0, 0, '2025-05-05 10:20:26', '2025-05-07 10:20:26', '2025-05-04 10:20:26', 100.00, 'manual', 2000.00, 'pending', '2025-05-03 10:20:26', 'Test Destination', 'Test Drop-off', 'TEST-20250506-50444', 1);

-- --------------------------------------------------------

--
-- Table structure for table `booking_logs`
--

CREATE TABLE `booking_logs` (
  `log_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `date_of_booking` date NOT NULL,
  `time` time NOT NULL,
  `booking_id` int(11) NOT NULL,
  `boat` varchar(255) NOT NULL,
  `price` decimal(10,2) NOT NULL,
  `admin_id` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `booking_logs`
--

INSERT INTO `booking_logs` (`log_id`, `user_id`, `date_of_booking`, `time`, `booking_id`, `boat`, `price`, `admin_id`) VALUES
(1, 1, '2025-01-09', '09:28:16', 1, 'Boat 1', 2000.00, 1),
(2, 2, '2025-02-02', '10:00:00', 2, 'Boat 2', 3500.00, 3);

-- --------------------------------------------------------

--
-- Table structure for table `booking_status_logs`
--

CREATE TABLE `booking_status_logs` (
  `log_id` int(11) NOT NULL,
  `booking_id` int(11) NOT NULL,
  `old_status` enum('pending','confirmed','cancelled') NOT NULL,
  `new_status` enum('pending','confirmed','cancelled') NOT NULL,
  `reason` text DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `customers`
--

CREATE TABLE `customers` (
  `customer_id` int(11) NOT NULL AUTO_INCREMENT,
  `first_name` varchar(255) NOT NULL,
  `last_name` varchar(255) NOT NULL,
  `age` int(11) DEFAULT NULL,
  `sex` varchar(10) DEFAULT NULL,
  `contact_number` varchar(20) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `address` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`customer_id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `customers`
--

INSERT INTO `customers` (`customer_id`, `first_name`, `last_name`, `age`, `sex`, `contact_number`, `email`, `address`) VALUES
(1, 'Ralph', 'Ramos', 21, 'male', '09345789658', '<EMAIL>', 'Carles, Iloilo'),
(2, 'Maria', 'Santos', 25, 'female', '09181234567', '<EMAIL>', 'Carles, Iloilo'),
(3, 'John', 'Doe', 30, 'male', '09175553333', '<EMAIL>', 'Balasan, Iloilo');

-- --------------------------------------------------------

--
-- Table structure for table `destinations`
--

CREATE TABLE `destinations` (
  `destination_id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  PRIMARY KEY (`destination_id`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `destinations`
--

INSERT INTO `destinations` (`destination_id`, `name`) VALUES
(1, 'Tumaquin Island'),
(2, 'Gigantes Island'),
(3, 'Sicogon Island'),
(4, 'Bayas Island'),
(5, 'Cabugao Gamay'),
(6, 'Antonia Beach'),
(7, 'Tangke Lagoon'),
(8, 'Agho Island'),
(9, 'Bantigue Sandbar'),
(10, 'Pan de Azucar');

-- --------------------------------------------------------

--
-- Table structure for table `login_attempts`
--

CREATE TABLE `login_attempts` (
  `id` int(11) NOT NULL,
  `ip_address` varchar(45) NOT NULL,
  `email` varchar(100) NOT NULL,
  `success` tinyint(1) NOT NULL DEFAULT 0,
  `attempt_time` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `login_attempts`
--

INSERT INTO `login_attempts` (`id`, `ip_address`, `email`, `success`, `attempt_time`) VALUES
(1, '::1', 'admin', 1, '2025-05-06 09:13:45'),
(2, '::1', 'admin', 1, '2025-05-06 09:22:01'),
(3, '::1', 'admin', 1, '2025-05-06 09:33:54'),
(4, '::1', 'admin', 1, '2025-05-06 09:58:43');

-- --------------------------------------------------------

--
-- Table structure for table `notifications`
--

CREATE TABLE `notifications` (
  `notification_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `message` text NOT NULL,
  `type` varchar(50) DEFAULT 'general',
  `reference_id` int(11) DEFAULT NULL,
  `is_read` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `notifications`
--

INSERT INTO `notifications` (`notification_id`, `user_id`, `message`, `type`, `reference_id`, `is_read`, `created_at`) VALUES
(1, 1, 'Test notification created at 2025-05-06 09:36:18', 'test', NULL, 1, '2025-05-06 09:36:18');

-- --------------------------------------------------------

--
-- Table structure for table `tourist`
--

CREATE TABLE `tourist` (
  `user_id` int(11) NOT NULL,
  `full_name` varchar(255) NOT NULL,
  `address` varchar(255) NOT NULL,
  `mobile_number` varchar(20) NOT NULL,
  `email_address` varchar(100) NOT NULL,
  `sex` varchar(10) NOT NULL,
  `birthdate` date NOT NULL,
  `date_of_tour` date NOT NULL,
  `number_of_pax` int(11) NOT NULL,
  `tour_destination` varchar(255) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `tourist`
--

INSERT INTO `tourist` (`user_id`, `full_name`, `address`, `mobile_number`, `email_address`, `sex`, `birthdate`, `date_of_tour`, `number_of_pax`, `tour_destination`, `created_at`, `updated_at`) VALUES
(1, 'Ralph Ramos', 'Poblacion, Carles, Iloilo', '09495969705', '<EMAIL>', 'Male', '2004-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:03:41', '2025-05-05 21:03:41'),
(2, 'Christian Lopez', 'Balasan, Iloilo', '09594598488', '<EMAIL>', 'Female', '2000-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:03:41', '2025-05-05 21:03:41'),
(3, 'John Doe', 'Balasan, Iloilo', '09175553333', '<EMAIL>', 'Male', '1995-05-05', '2025-05-05', 1, 'Not specified', '2025-05-05 21:03:41', '2025-05-05 21:03:41');

-- --------------------------------------------------------

--
-- Table structure for table `tourist_origin_stats`
--

CREATE TABLE `tourist_origin_stats` (
  `stat_id` int(11) NOT NULL,
  `report_id` int(11) NOT NULL,
  `origin_location` varchar(255) NOT NULL,
  `tourist_count` int(11) NOT NULL DEFAULT 0,
  `percentage` decimal(5,2) NOT NULL DEFAULT 0.00
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `tourist_origin_stats`
--

INSERT INTO `tourist_origin_stats` (`stat_id`, `report_id`, `origin_location`, `tourist_count`, `percentage`) VALUES
(1, 1, 'Iloilo', 150, 60.00),
(2, 1, 'Manila', 50, 20.00),
(3, 1, 'Cebu', 30, 12.00),
(4, 1, 'Other', 20, 8.00);

-- --------------------------------------------------------

--
-- Table structure for table `tourist_reports`
--

CREATE TABLE `tourist_reports` (
  `report_id` int(11) NOT NULL,
  `report_date` date NOT NULL,
  `report_type` enum('daily','weekly','monthly','yearly','custom') NOT NULL,
  `total_tourists` int(11) NOT NULL DEFAULT 0,
  `regular_tourists` int(11) NOT NULL DEFAULT 0,
  `discounted_tourists` int(11) NOT NULL DEFAULT 0,
  `children_tourists` int(11) NOT NULL DEFAULT 0,
  `infants_tourists` int(11) NOT NULL DEFAULT 0,
  `total_revenue` decimal(10,2) NOT NULL DEFAULT 0.00,
  `environmental_fee_collected` decimal(10,2) NOT NULL DEFAULT 0.00,
  `most_visited_destination` varchar(255) DEFAULT NULL,
  `generated_by` int(11) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `tourist_reports`
--

INSERT INTO `tourist_reports` (`report_id`, `report_date`, `report_type`, `total_tourists`, `regular_tourists`, `discounted_tourists`, `children_tourists`, `infants_tourists`, `total_revenue`, `environmental_fee_collected`, `most_visited_destination`, `generated_by`, `created_at`, `updated_at`) VALUES
(1, '2025-04-30', 'monthly', 250, 180, 40, 20, 10, 125000.00, 10000.00, 'Gigantes Island', 1, '2025-05-01 09:00:00', '2025-05-01 09:00:00');

-- --------------------------------------------------------

--
-- Table structure for table `tourist_report_details`
--

CREATE TABLE `tourist_report_details` (
  `detail_id` int(11) NOT NULL,
  `report_id` int(11) NOT NULL,
  `destination_id` int(11) NOT NULL,
  `destination_name` varchar(255) NOT NULL,
  `tourist_count` int(11) NOT NULL DEFAULT 0,
  `revenue` decimal(10,2) NOT NULL DEFAULT 0.00
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `tourist_report_details`
--

INSERT INTO `tourist_report_details` (`detail_id`, `report_id`, `destination_id`, `destination_name`, `tourist_count`, `revenue`) VALUES
(1, 1, 2, 'Gigantes Island', 100, 50000.00),
(2, 1, 3, 'Sicogon Island', 80, 40000.00),
(3, 1, 5, 'Cabugao Gamay', 70, 35000.00);

--
-- Indexes for dumped tables
--

--
-- Indexes for table `activity_logs`
--
ALTER TABLE `activity_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `activity_logs_ibfk_1` (`admin_id`);

--
-- Indexes for table `admins`
--
-- Primary key already defined in CREATE TABLE statement

--
-- Indexes for table `boats`
--
-- Primary key already defined in CREATE TABLE statement

--
-- Indexes for table `boat_availability_dates`
--
ALTER TABLE `boat_availability_dates`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `boat_date` (`boat_id`,`available_date`),
  ADD KEY `boat_availability_dates_ibfk_2` (`added_by`);

--
-- Indexes for table `boat_reservations`
--
ALTER TABLE `boat_reservations`
  ADD PRIMARY KEY (`booking_id`);

--
-- Indexes for table `bookings`
--
-- Primary key already defined in CREATE TABLE statement

--
-- Indexes for table `booking_logs`
--
ALTER TABLE `booking_logs`
  ADD PRIMARY KEY (`log_id`),
  ADD KEY `booking_logs_ibfk_1` (`user_id`),
  ADD KEY `booking_logs_ibfk_2` (`admin_id`);

--
-- Indexes for table `booking_status_logs`
--
ALTER TABLE `booking_status_logs`
  ADD PRIMARY KEY (`log_id`),
  ADD KEY `booking_id` (`booking_id`),
  ADD KEY `created_by` (`created_by`);

--
-- Indexes for table `customers`
--
-- Primary key already defined in CREATE TABLE statement

--
-- Indexes for table `destinations`
--
-- Primary key already defined in CREATE TABLE statement

--
-- Indexes for table `login_attempts`
--
ALTER TABLE `login_attempts`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `notifications`
--
ALTER TABLE `notifications`
  ADD PRIMARY KEY (`notification_id`),
  ADD KEY `notifications_ibfk_1` (`user_id`);

--
-- Indexes for table `tourist`
--
ALTER TABLE `tourist`
  ADD PRIMARY KEY (`user_id`);

--
-- Indexes for table `tourist_origin_stats`
--
ALTER TABLE `tourist_origin_stats`
  ADD PRIMARY KEY (`stat_id`),
  ADD KEY `tourist_origin_stats_ibfk_1` (`report_id`);

--
-- Indexes for table `tourist_reports`
--
ALTER TABLE `tourist_reports`
  ADD PRIMARY KEY (`report_id`),
  ADD KEY `tourist_reports_ibfk_1` (`generated_by`);

--
-- Indexes for table `tourist_report_details`
--
ALTER TABLE `tourist_report_details`
  ADD PRIMARY KEY (`detail_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `activity_logs`
--
ALTER TABLE `activity_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=43;

--
-- AUTO_INCREMENT for table `admins`
--
ALTER TABLE `admins`
  MODIFY `admin_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `boats`
--
ALTER TABLE `boats`
  MODIFY `boat_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `boat_availability_dates`
--
ALTER TABLE `boat_availability_dates`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `boat_reservations`
--
ALTER TABLE `boat_reservations`
  MODIFY `booking_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=96;

--
-- AUTO_INCREMENT for table `bookings`
--
ALTER TABLE `bookings`
  MODIFY `booking_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=27;

--
-- AUTO_INCREMENT for table `booking_logs`
--
ALTER TABLE `booking_logs`
  MODIFY `log_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `booking_status_logs`
--
ALTER TABLE `booking_status_logs`
  MODIFY `log_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `customers`
--
ALTER TABLE `customers`
  MODIFY `customer_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `destinations`
--
ALTER TABLE `destinations`
  MODIFY `destination_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `login_attempts`
--
ALTER TABLE `login_attempts`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `notifications`
--
ALTER TABLE `notifications`
  MODIFY `notification_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `tourist`
--
ALTER TABLE `tourist`
  MODIFY `user_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `tourist_origin_stats`
--
ALTER TABLE `tourist_origin_stats`
  MODIFY `stat_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `tourist_reports`
--
ALTER TABLE `tourist_reports`
  MODIFY `report_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `tourist_report_details`
--
ALTER TABLE `tourist_report_details`
  MODIFY `detail_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `activity_logs`
--
ALTER TABLE `activity_logs`
  ADD CONSTRAINT `activity_logs_ibfk_1` FOREIGN KEY (`admin_id`) REFERENCES `admins` (`admin_id`) ON DELETE CASCADE;

--
-- Constraints for table `boat_availability_dates`
--
ALTER TABLE `boat_availability_dates`
  ADD CONSTRAINT `boat_availability_dates_ibfk_1` FOREIGN KEY (`boat_id`) REFERENCES `boats` (`boat_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `boat_availability_dates_ibfk_2` FOREIGN KEY (`added_by`) REFERENCES `admins` (`admin_id`) ON DELETE SET NULL;

--
-- Constraints for table `booking_logs`
--
ALTER TABLE `booking_logs`
  ADD CONSTRAINT `booking_logs_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `tourist` (`user_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `booking_logs_ibfk_2` FOREIGN KEY (`admin_id`) REFERENCES `admins` (`admin_id`) ON DELETE SET NULL;

--
-- Constraints for table `booking_status_logs`
--
ALTER TABLE `booking_status_logs`
  ADD CONSTRAINT `booking_status_logs_ibfk_1` FOREIGN KEY (`booking_id`) REFERENCES `bookings` (`booking_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `booking_status_logs_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `admins` (`admin_id`) ON DELETE SET NULL;

--
-- Constraints for table `notifications`
--
ALTER TABLE `notifications`
  ADD CONSTRAINT `notifications_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `admins` (`admin_id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `tourist_origin_stats`
--
ALTER TABLE `tourist_origin_stats`
  ADD CONSTRAINT `tourist_origin_stats_ibfk_1` FOREIGN KEY (`report_id`) REFERENCES `tourist_reports` (`report_id`) ON DELETE CASCADE;

--
-- Constraints for table `tourist_reports`
--
ALTER TABLE `tourist_reports`
  ADD CONSTRAINT `tourist_reports_ibfk_1` FOREIGN KEY (`generated_by`) REFERENCES `admins` (`admin_id`) ON DELETE SET NULL;

-- Re-enable foreign key checks and security settings
SET FOREIGN_KEY_CHECKS = 1;
SET SQL_SAFE_UPDATES = 1;
SET SQL_MODE=@OLD_SQL_MODE;

COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
