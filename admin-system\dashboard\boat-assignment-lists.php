<?php
session_start();
include('includes/config.php');

if(strlen($_SESSION['aid'])==0) {
    header('location:../login/admin-login.php');
    exit();
}

// Function to check and reconnect to database if needed
function ensureConnection($connection) {
    // If connection is false (already failed), try to create a new one
    if ($connection === false) {
        // Get database credentials from config
        $db_host = DB_HOST;
        $db_user = DB_USER;
        $db_pass = DB_PASS;
        $db_name = DB_NAME;

        try {
            // Create a new connection
            $new_connection = new mysqli($db_host, $db_user, $db_pass, $db_name);

            // Check if connection was successful
            if ($new_connection->connect_error) {
                // Log error and show friendly message
                error_log("Database reconnection failed: " . $new_connection->connect_error);
                echo "<div class='alert alert-danger'>Database connection error. Please try refreshing the page.</div>";
                return false;
            }

            // Return the new connection
            return $new_connection;
        } catch (Exception $e) {
            error_log("Database reconnection exception: " . $e->getMessage());
            return false;
        }
    }

    // If connection exists but might be stale, check it
    if ($connection && is_object($connection)) {
        try {
            // Check if connection is still active
            if (!$connection->ping()) {
                // Connection is not active, try to reconnect
                $connection->close();

                // Get database credentials from config
                $db_host = DB_HOST;
                $db_user = DB_USER;
                $db_pass = DB_PASS;
                $db_name = DB_NAME;

                // Create a new connection
                $new_connection = new mysqli($db_host, $db_user, $db_pass, $db_name);

                // Check if connection was successful
                if ($new_connection->connect_error) {
                    // Log error and show friendly message
                    error_log("Database reconnection failed: " . $new_connection->connect_error);
                    echo "<div class='alert alert-danger'>Database connection error. Please try refreshing the page.</div>";
                    return false;
                }

                // Return the new connection
                return $new_connection;
            }
        } catch (Exception $e) {
            error_log("Connection ping exception: " . $e->getMessage());
            return false;
        }
    }

    // Connection is still active or was null
    return $connection;
}

// Ensure we have a valid database connection
$con = ensureConnection($con);
if (!$con) {
    // If connection failed, show error and exit
    exit();
}

// Get the selected date from GET parameter, default to today
$selected_date = isset($_GET['date']) ? $_GET['date'] : date('Y-m-d');

// Format for display
$formatted_date = date('l, F j, Y', strtotime($selected_date));

// Calculate previous and next day
$prev_day = date('Y-m-d', strtotime($selected_date . ' -1 day'));
$next_day = date('Y-m-d', strtotime($selected_date . ' +1 day'));

// Get all boats with bookings for the selected date
try {
    $sql = "SELECT DISTINCT
        bt.boat_id,
        bt.name as boat_name
    FROM
        bookings b
    JOIN
        boats bt ON b.boat_id = bt.boat_id
    WHERE
        DATE(b.start_date) = ?
    ORDER BY
        bt.boat_id ASC";

    $stmt = $con->prepare($sql);
    if (!$stmt) {
        throw new Exception("Prepare failed: " . $con->error);
    }

    $stmt->bind_param("s", $selected_date);
    $stmt->execute();
    $boats_result = $stmt->get_result();
} catch (Exception $e) {
    // Log the error
    error_log("Database error in boat-assignment-lists.php: " . $e->getMessage());

    // Try to reconnect and retry once
    $con = ensureConnection($con);
    if ($con) {
        try {
            $stmt = $con->prepare($sql);
            $stmt->bind_param("s", $selected_date);
            $stmt->execute();
            $boats_result = $stmt->get_result();
        } catch (Exception $e) {
            // If it fails again, show error message
            echo "<div class='alert alert-danger'>Error loading boat data. Please try refreshing the page.</div>";
            $boats_result = false;
        }
    }
}

// Store boats in an array
$boats = [];
while ($boat_row = $boats_result->fetch_assoc()) {
    $boats[] = $boat_row;
}

// For each boat, get all bookings
$boat_assignments = [];
foreach ($boats as $boat) {
    $boat_id = $boat['boat_id'];

    // Ensure connection is still valid
    $con = ensureConnection($con);
    if (!$con) {
        continue; // Skip this boat if connection failed
    }

    try {
        $sql = "SELECT
            b.booking_id,
            b.booking_code,
            b.start_date,
            b.no_of_pax,
            b.booking_status,
            COALESCE(c.first_name, b.first_name) as first_name,
            COALESCE(c.last_name, b.last_name) as last_name,
            COALESCE(c.contact_number, b.contact_number) as contact_number,
            COALESCE(c.address, b.address) as address,
            d.name as destination_name
        FROM
            bookings b
        LEFT JOIN
            customers c ON b.customer_id = c.customer_id
        LEFT JOIN
            destinations d ON b.destination_id = d.destination_id
        WHERE
            DATE(b.start_date) = ? AND b.boat_id = ? AND (b.booking_status = 'confirmed' OR b.booking_status = 'accepted')
        ORDER BY
            TIME(b.start_date) ASC";

        $stmt = $con->prepare($sql);
        if (!$stmt) {
            throw new Exception("Prepare failed: " . $con->error);
        }

        $stmt->bind_param("si", $selected_date, $boat_id);
        $stmt->execute();
        $bookings_result = $stmt->get_result();

        // Store bookings for this boat
        $bookings = [];
        $total_pax = 0;
        while ($booking_row = $bookings_result->fetch_assoc()) {
            $bookings[] = $booking_row;
            $total_pax += $booking_row['no_of_pax'];
        }

        // Add to boat assignments array
        $boat_assignments[$boat_id] = [
            'boat_name' => $boat['boat_name'],
            'bookings' => $bookings,
            'total_pax' => $total_pax
        ];
    } catch (Exception $e) {
        // Log the error
        error_log("Database error in boat-assignment-lists.php (boat ID: $boat_id): " . $e->getMessage());

        // Try to reconnect and retry once
        $con = ensureConnection($con);
        if ($con) {
            try {
                $stmt = $con->prepare($sql);
                $stmt->bind_param("si", $selected_date, $boat_id);
                $stmt->execute();
                $bookings_result = $stmt->get_result();

                // Store bookings for this boat
                $bookings = [];
                $total_pax = 0;
                while ($booking_row = $bookings_result->fetch_assoc()) {
                    $bookings[] = $booking_row;
                    $total_pax += $booking_row['no_of_pax'];
                }

                // Add to boat assignments array
                $boat_assignments[$boat_id] = [
                    'boat_name' => $boat['boat_name'],
                    'bookings' => $bookings,
                    'total_pax' => $total_pax
                ];
            } catch (Exception $e) {
                // If it fails again, add empty data for this boat
                $boat_assignments[$boat_id] = [
                    'boat_name' => $boat['boat_name'],
                    'bookings' => [],
                    'total_pax' => 0
                ];
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Boat Assignment Lists | Boat Booking System</title>
  <link rel="stylesheet" href="plugins/fontawesome-free/css/all.min.css">
  <link rel="stylesheet" href="dist/css/adminlte.min.css">
  <link rel="stylesheet" href="plugins/datatables-bs4/css/dataTables.bootstrap4.min.css">
  <link rel="stylesheet" href="plugins/datatables-responsive/css/responsive.bootstrap4.min.css">
  <link rel="stylesheet" href="plugins/datatables-buttons/css/buttons.bootstrap4.min.css">
  <style>
    .date-navigation {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
    }
    .date-picker {
      display: flex;
      align-items: center;
    }
    .date-picker input {
      margin-right: 10px;
    }
    .boat-card {
      margin-bottom: 30px;
      break-inside: avoid;
    }
    .boat-header {
      background-color: #f4f6f9;
      padding: 15px;
      border-radius: 5px 5px 0 0;
      border: 1px solid #dee2e6;
      border-bottom: none;
    }
    .boat-title {
      font-size: 1.25rem;
      font-weight: bold;
      margin: 0;
    }
    .boat-info {
      display: flex;
      justify-content: space-between;
      margin-top: 5px;
    }
    .boat-body {
      padding: 0;
      border: 1px solid #dee2e6;
      border-radius: 0 0 5px 5px;
    }
    .tourist-table {
      width: 100%;
      margin-bottom: 0;
    }
    .tourist-table th, .tourist-table td {
      padding: 12px 15px;
    }
    .print-header {
      text-align: center;
      margin-bottom: 20px;
    }
    .print-header h2 {
      margin-bottom: 5px;
    }
    .print-header p {
      margin-bottom: 0;
      font-size: 1.1rem;
    }
    .print-footer {
      text-align: center;
      margin-top: 30px;
      font-size: 0.9rem;
      color: #6c757d;
    }
    .print-button {
      margin-bottom: 10px;
    }
    @media print {
      .no-print {
        display: none !important;
      }
      .content-wrapper {
        margin-left: 0 !important;
        padding-top: 0 !important;
      }
      .boat-card {
        page-break-inside: avoid;
      }
      .main-footer {
        display: none;
      }
    }
  </style>
</head>
<body class="hold-transition sidebar-mini">
<div class="wrapper">
  <?php include_once('includes/navbar.php'); ?>
  <?php include_once('includes/sidebar.php'); ?>

  <div class="content-wrapper">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6">
            <h1>Boat Assignment Lists</h1>
          </div>
        </div>
      </div>
    </section>

    <section class="content">
      <div class="container-fluid">
        <!-- Date Navigation -->
        <div class="card no-print">
          <div class="card-body">
            <div class="date-navigation">
              <a href="?date=<?php echo $prev_day; ?>" class="btn btn-default">
                <i class="fas fa-chevron-left"></i> Previous Day
              </a>

              <div class="date-picker">
                <form action="" method="get" id="dateForm">
                  <div class="input-group">
                    <input type="date" name="date" id="datePicker" class="form-control" value="<?php echo $selected_date; ?>">
                    <div class="input-group-append">
                      <button type="submit" class="btn btn-primary">Go</button>
                    </div>
                  </div>
                </form>
              </div>

              <a href="?date=<?php echo $next_day; ?>" class="btn btn-default">
                Next Day <i class="fas fa-chevron-right"></i>
              </a>
            </div>
          </div>
        </div>

        <!-- No buttons here anymore, using DataTables buttons instead -->

        <!-- Print Header -->
        <div class="print-header">
          <h2>Boat Assignment Lists</h2>
          <p><?php echo $formatted_date; ?></p>
        </div>

        <!-- Boat Assignment Lists -->
        <div class="row">
          <?php if (empty($boats)): ?>
            <div class="col-12">
              <div class="alert alert-info">
                <i class="fas fa-info-circle"></i> No boats scheduled for this day.
              </div>
            </div>
          <?php else: ?>
            <?php foreach ($boat_assignments as $boat_id => $data): ?>
              <div class="col-12">
                <div class="boat-card">
                  <div class="boat-header">
                    <h3 class="boat-title">
                      <i class="fas fa-ship"></i> <?php echo htmlspecialchars($data['boat_name']); ?>
                    </h3>
                    <div class="boat-info">
                      <span>
                        <strong>Date:</strong> <?php echo $formatted_date; ?>
                      </span>
                      <span>
                        <strong>Total Passengers:</strong> <?php echo $data['total_pax']; ?>
                      </span>
                    </div>
                  </div>
                  <div class="boat-body">
                    <?php if (empty($data['bookings'])): ?>
                      <div class="alert alert-warning m-3">
                        <i class="fas fa-exclamation-triangle"></i> No confirmed bookings for this boat.
                      </div>
                    <?php else: ?>
                      <div class="p-3">
                        <table id="example<?php echo $boat_id; ?>" class="table table-bordered table-striped">
                          <thead>
                            <tr>
                              <th>#</th>
                              <th>Tourist Name</th>
                              <th>Contact Number</th>
                              <th>Address</th>
                              <th>Destination</th>
                              <th>Time</th>
                              <th>Pax</th>
                              <th>Booking Code</th>
                            </tr>
                          </thead>
                          <tbody>
                            <?php $count = 1; ?>
                            <?php foreach ($data['bookings'] as $booking): ?>
                              <tr>
                                <td><?php echo $count++; ?></td>
                                <td><?php echo htmlspecialchars($booking['first_name'] . ' ' . $booking['last_name']); ?></td>
                                <td><?php echo htmlspecialchars($booking['contact_number']); ?></td>
                                <td><?php echo htmlspecialchars($booking['address']); ?></td>
                                <td><?php echo htmlspecialchars($booking['destination_name']); ?></td>
                                <td><?php echo date('h:i A', strtotime($booking['start_date'])); ?></td>
                                <td><?php echo $booking['no_of_pax']; ?></td>
                                <td><?php echo htmlspecialchars($booking['booking_code']); ?></td>
                              </tr>
                            <?php endforeach; ?>
                          </tbody>
                        </table>
                      </div>
                    <?php endif; ?>
                  </div>
                </div>

                <!-- Individual Print Button -->
                <div class="no-print mb-4">
                  <button type="button" class="btn btn-outline-primary" onclick="printSingleBoat(<?php echo $boat_id; ?>)">
                    <i class="fas fa-print"></i> Print This Boat List Only
                  </button>
                </div>
              </div>
            <?php endforeach; ?>
          <?php endif; ?>
        </div>

        <!-- Print Footer -->
        <div class="print-footer">
          <p>Generated on <?php echo date('F j, Y, g:i a'); ?></p>
          <p>Municipality of Carles Tourism Office</p>
        </div>
      </div>
    </section>
  </div>

  <?php include_once('includes/footer.php'); ?>
</div>

<script src="plugins/jquery/jquery.min.js"></script>
<script src="plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
<script src="dist/js/adminlte.min.js"></script>
<script src="plugins/datatables/jquery.dataTables.min.js"></script>
<script src="plugins/datatables-bs4/js/dataTables.bootstrap4.min.js"></script>
<script src="plugins/datatables-responsive/js/responsive.bootstrap4.min.js"></script>
<script src="plugins/datatables-buttons/js/dataTables.buttons.min.js"></script>
<script src="plugins/datatables-buttons/js/buttons.bootstrap4.min.js"></script>
<script src="plugins/jszip/jszip.min.js"></script>
<script src="plugins/pdfmake/pdfmake.min.js"></script>
<script src="plugins/pdfmake/vfs_fonts.js"></script>
<script src="plugins/datatables-buttons/js/buttons.html5.min.js"></script>
<script src="plugins/datatables-buttons/js/buttons.print.min.js"></script>
<script src="plugins/datatables-buttons/js/buttons.colVis.min.js"></script>
<script>
$(function () {
  // Auto-submit when date changes
  $('#datePicker').change(function() {
    $('#dateForm').submit();
  });

  // Initialize DataTables with export buttons
  <?php foreach ($boat_assignments as $boat_id => $data): ?>
    $("#example<?php echo $boat_id; ?>").DataTable({
      "responsive": true,
      "lengthChange": true,
      "autoWidth": false,
      "buttons": ["copy", "csv", "excel", "pdf", "print", "colvis"],
      "order": [[0, "asc"]],
      "pageLength": 10
    }).buttons().container().appendTo('#example<?php echo $boat_id; ?>_wrapper .col-md-6:eq(0)');
  <?php endforeach; ?>
});

// Function to print a single boat list
function printSingleBoat(boatId) {
  // Hide all boat cards
  $('.boat-card').hide();

  // Show only the selected boat card
  $('.boat-card').eq(boatId - 1).show();

  // Print
  window.print();

  // Show all boat cards again
  $('.boat-card').show();
}
</script>
</body>
</html>
