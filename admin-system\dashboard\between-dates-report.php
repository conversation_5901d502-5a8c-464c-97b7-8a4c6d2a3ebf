    <?php
session_start();
include('includes/config.php');

if(strlen($_SESSION['aid'])==0) {
    header('location:../login/admin-login.php');
    exit();
}

// Get date range from POST if submitted
$from_date = isset($_POST['from_date']) ? $_POST['from_date'] : date('Y-m-d');
$to_date = isset($_POST['to_date']) ? $_POST['to_date'] : date('Y-m-d');

// Get statistics
$total_bookings = 0;
$accepted_bookings = 0;
$rejected_bookings = 0;
$total_revenue = 0;

$sql = "SELECT
    COUNT(*) as total,
    SUM(CASE WHEN booking_status = 'accepted' OR booking_status = 'confirmed' THEN 1 ELSE 0 END) as accepted,
    SUM(CASE WHEN booking_status = 'rejected' OR booking_status = 'cancelled' THEN 1 ELSE 0 END) as rejected,
    SUM(CASE WHEN booking_status = 'accepted' OR booking_status = 'confirmed' THEN total ELSE 0 END) as revenue
FROM bookings
WHERE DATE(booking_time) BETWEEN ? AND ?";
$stmt = $con->prepare($sql);
$stmt->bind_param("ss", $from_date, $to_date);
$stmt->execute();
$stats_query = $stmt->get_result();

if($stats_query) {
    $stats = mysqli_fetch_assoc($stats_query);
    $total_bookings = $stats['total'] ?? 0;
    $accepted_bookings = $stats['accepted'] ?? 0;
    $rejected_bookings = $stats['rejected'] ?? 0;
    $total_revenue = $stats['revenue'] ?? 0;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Between Dates Report | Boat Booking System</title>
    <link rel="stylesheet" href="plugins/fontawesome-free/css/all.min.css">
    <link rel="stylesheet" href="dist/css/adminlte.min.css">
    <link rel="stylesheet" href="plugins/daterangepicker/daterangepicker.css">
</head>
<body class="hold-transition sidebar-mini">
<div class="wrapper">
    <?php include_once('includes/navbar.php'); ?>
    <?php include_once('includes/sidebar.php'); ?>

    <div class="content-wrapper">
        <section class="content-header">
            <div class="container-fluid">
                <div class="row mb-2">
                    <div class="col-sm-6">
                        <h1>Between Dates Report</h1>
                    </div>
                </div>
            </div>
        </section>

        <section class="content">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-md-3">
                        <div class="small-box bg-info">
                            <div class="inner">
                                <h3><?php echo $total_bookings; ?></h3>
                                <p>Total Bookings</p>
                            </div>
                            <div class="icon">
                                <i class="fas fa-calendar-check"></i>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="small-box bg-success">
                            <div class="inner">
                                <h3><?php echo $accepted_bookings; ?></h3>
                                <p>Accepted</p>
                            </div>
                            <div class="icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="small-box bg-danger">
                            <div class="inner">
                                <h3><?php echo $rejected_bookings; ?></h3>
                                <p>Rejected</p>
                            </div>
                            <div class="icon">
                                <i class="fas fa-times-circle"></i>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="small-box bg-warning">
                            <div class="inner">
                                <h3>₱<?php echo number_format($total_revenue, 2); ?></h3>
                                <p>Total Revenue</p>
                            </div>
                            <div class="icon">
                                <i class="fas fa-money-bill-wave"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Between Dates Booking Report</h3>
                    </div>
                    <div class="card-body">
                        <form method="post" action="" id="reportForm">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label>From Date</label>
                                        <input type="date" class="form-control" name="from_date" value="<?php echo $from_date; ?>" required>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label>To Date</label>
                                        <input type="date" class="form-control" name="to_date" value="<?php echo $to_date; ?>" required>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group" style="margin-top: 32px;">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-search"></i> Generate Report
                                        </button>
                                        <button type="button" class="btn btn-success" onclick="printReport()">
                                            <i class="fas fa-print"></i> Print Report
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>

                        <div class="table-responsive mt-4">
                            <table class="table table-bordered table-striped" id="reportTable">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>Booking ID</th>
                                        <th>Customer Name</th>
                                        <th>Date</th>
                                        <th>Status</th>
                                        <th>Amount</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $sql = "SELECT
                                        b.booking_id,
                                        b.booking_code,
                                        CONCAT(b.first_name, ' ', b.last_name) as customer_name,
                                        b.booking_time,
                                        b.booking_status,
                                        b.total
                                    FROM bookings b
                                    WHERE DATE(b.booking_time) BETWEEN ? AND ?
                                    ORDER BY b.booking_time DESC";
                                    $stmt = $con->prepare($sql);
                                    $stmt->bind_param("ss", $from_date, $to_date);
                                    $stmt->execute();
                                    $query = $stmt->get_result();

                                    if($query) {
                                        $cnt = 1;
                                        while($row = mysqli_fetch_assoc($query)) {
                                            $status_class = '';
                                            $status_text = '';

                                            switch($row['booking_status']) {
                                                case 'pending':
                                                    $status_class = 'badge-warning';
                                                    $status_text = 'Pending';
                                                    break;
                                                case 'accepted':
                                                case 'confirmed':
                                                    $status_class = 'badge-success';
                                                    $status_text = ucfirst($row['booking_status']);
                                                    break;
                                                case 'rejected':
                                                case 'cancelled':
                                                    $status_class = 'badge-danger';
                                                    $status_text = ucfirst($row['booking_status']);
                                                    break;
                                                default:
                                                    $status_class = 'badge-info';
                                                    $status_text = ucfirst($row['booking_status']);
                                            }
                                            ?>
                                            <tr>
                                                <td><?php echo $cnt++; ?></td>
                                                <td><?php echo htmlspecialchars($row['booking_code']); ?></td>
                                                <td><?php echo htmlspecialchars($row['customer_name']); ?></td>
                                                <td><?php echo date('M d, Y', strtotime($row['booking_time'])); ?></td>
                                                <td><span class="badge <?php echo $status_class; ?>"><?php echo $status_text; ?></span></td>
                                                <td>₱<?php echo number_format($row['total'], 2); ?></td>
                                            </tr>
                                            <?php
                                        }
                                    }
                                    ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
</div>

<script src="plugins/jquery/jquery.min.js"></script>
<script src="plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
<script src="dist/js/adminlte.min.js"></script>
<script src="plugins/datatables/jquery.dataTables.min.js"></script>
<script src="plugins/datatables-bs4/js/dataTables.bootstrap4.min.js"></script>
<script>
$(function () {
    $('#reportTable').DataTable({
        "paging": true,
        "lengthChange": false,
        "searching": true,
        "ordering": true,
        "info": true,
        "autoWidth": false,
        "responsive": true,
    });
});

function printReport() {
    var printWindow = window.open('', '_blank');
    printWindow.document.write('<html><head><title>Booking Report</title>');
    printWindow.document.write('<link rel="stylesheet" href="plugins/bootstrap/css/bootstrap.min.css">');
    printWindow.document.write('<style>@media print { .no-print { display: none; } }</style>');
    printWindow.document.write('</head><body>');

    // Add report header
    printWindow.document.write('<div class="container mt-4">');
    printWindow.document.write('<h2 class="text-center">Booking Report</h2>');
    printWindow.document.write('<p class="text-center">From: <?php echo date("M d, Y", strtotime($from_date)); ?> To: <?php echo date("M d, Y", strtotime($to_date)); ?></p>');

    // Add statistics
    printWindow.document.write('<div class="row mb-4">');
    printWindow.document.write('<div class="col-md-3"><strong>Total Bookings:</strong> <?php echo $total_bookings; ?></div>');
    printWindow.document.write('<div class="col-md-3"><strong>Accepted:</strong> <?php echo $accepted_bookings; ?></div>');
    printWindow.document.write('<div class="col-md-3"><strong>Rejected:</strong> <?php echo $rejected_bookings; ?></div>');
    printWindow.document.write('<div class="col-md-3"><strong>Total Revenue:</strong> ₱<?php echo number_format($total_revenue, 2); ?></div>');
    printWindow.document.write('</div>');

    // Add table
    printWindow.document.write('<table class="table table-bordered">');
    printWindow.document.write('<thead><tr><th>#</th><th>Booking ID</th><th>Customer Name</th><th>Date</th><th>Status</th><th>Amount</th></tr></thead>');
    printWindow.document.write('<tbody>');

    // Add table rows
    <?php
    $sql = "SELECT
        b.booking_id,
        b.booking_code,
        CONCAT(b.first_name, ' ', b.last_name) as customer_name,
        b.booking_time,
        b.booking_status,
        b.total
    FROM bookings b
    WHERE DATE(b.booking_time) BETWEEN ? AND ?
    ORDER BY b.booking_time DESC";
    $stmt = $con->prepare($sql);
    $stmt->bind_param("ss", $from_date, $to_date);
    $stmt->execute();
    $query = $stmt->get_result();

    if($query) {
        $cnt = 1;
        while($row = mysqli_fetch_assoc($query)) {
            $status_text = ucfirst($row['booking_status']);
            echo "printWindow.document.write('<tr>');\n";
            echo "printWindow.document.write('<td>".$cnt++."</td>');\n";
            echo "printWindow.document.write('<td>".$row['booking_code']."</td>');\n";
            echo "printWindow.document.write('<td>".$row['customer_name']."</td>');\n";
            echo "printWindow.document.write('<td>".date('M d, Y', strtotime($row['booking_time']))."</td>');\n";
            echo "printWindow.document.write('<td>".$status_text."</td>');\n";
            echo "printWindow.document.write('<td>₱".number_format($row['total'], 2)."</td>');\n";
            echo "printWindow.document.write('</tr>');\n";
        }
    }
    ?>

    printWindow.document.write('</tbody></table>');
    printWindow.document.write('</div>');
    printWindow.document.write('</body></html>');
    printWindow.document.close();
    printWindow.print();
}
</script>
</body>
</html>
