# PowerShell script to update admin login paths in dashboard files

$files = @(
    "admin-system\dashboard\all-reservations.php",
    "admin-system\dashboard\background-settings.php", 
    "admin-system\dashboard\between-dates-report.php",
    "admin-system\dashboard\boat-assignment-lists.php",
    "admin-system\dashboard\booking-calendar.php",
    "admin-system\dashboard\bw-dates-report.php",
    "admin-system\dashboard\bwdates-report-details.php",
    "admin-system\dashboard\daily-schedule-new.php",
    "admin-system\dashboard\daily-schedule.php",
    "admin-system\dashboard\edit-booking.php",
    "admin-system\dashboard\manage-tables.php",
    "admin-system\dashboard\pending-bookings.php",
    "admin-system\dashboard\profile.php",
    "admin-system\dashboard\reject-booking.php",
    "admin-system\dashboard\rejected-bookings.php",
    "admin-system\dashboard\tourist-report.php",
    "admin-system\dashboard\view-booking.php"
)

foreach ($file in $files) {
    if (Test-Path $file) {
        Write-Host "Updating $file"
        (Get-Content $file) -replace "../../../../php/pages/admin-login\.php", "../login/admin-login.php" | Set-Content $file
        (Get-Content $file) -replace "../../../../php/pages/loginadmin/admin-login\.php", "../login/admin-login.php" | Set-Content $file
    }
}

# Update get-booking-details.php with its specific path
$file = "admin-system\dashboard\get-booking-details.php"
if (Test-Path $file) {
    Write-Host "Updating $file"
    (Get-Content $file) -replace "../../../../php/pages/loginadmin/admin-login\.php", "../login/admin-login.php" | Set-Content $file
}

Write-Host "All files updated successfully!"
