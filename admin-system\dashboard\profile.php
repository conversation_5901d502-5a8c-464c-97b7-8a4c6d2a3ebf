<?php
session_start();
// Database Connection
include('includes/config.php');
//Validating Session
if(strlen($_SESSION['aid'])==0)
{
  header('location:../login/admin-login.php');
  exit;
}
// Helper: Get/set persistent image map
function get_profile_image_map($file) {
  if(file_exists($file)) {
    $json = file_get_contents($file);
    $data = json_decode($json, true);
    if(is_array($data)) return $data;
  }
  return [];
}
function set_profile_image_map($file, $data) {
  file_put_contents($file, json_encode($data));
}
// Helper: Insert activity log
function add_activity_log($con, $adminid, $activity) {
  $adminid = intval($adminid);
  $activity = mysqli_real_escape_string($con, $activity);
  mysqli_query($con, "INSERT INTO activity_logs (admin_id, activity) VALUES ('$adminid', '$activity')");
}

$profile_map_file = __DIR__ . '/uploads/profile_images/profile_images_map.json';

// Handle profile image upload BEFORE any output
if(isset($_POST['upload_image'])){
  if(isset($_FILES['profile_image']) && $_FILES['profile_image']['error'] === UPLOAD_ERR_OK){
    $file = $_FILES['profile_image'];
    $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
    $file_type = mime_content_type($file['tmp_name']);
    if(!in_array($file_type, $allowed_types)){
      echo "<script>alert('Invalid file type. Only JPG, PNG, and GIF are allowed.');</script>";
    } else {
      $max_size = 5 * 1024 * 1024; // 5MB
      if($file['size'] > $max_size){
        echo "<script>alert('File size too large. Maximum is 5MB.');</script>";
      } else {
        $adminid = intval($_SESSION['aid']);
        $upload_dir = __DIR__ . '/uploads/profile_images/';
        if(!file_exists($upload_dir)){
          mkdir($upload_dir, 0777, true);
        }
        $file_extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $new_filename = 'admin_' . $adminid . '_' . time() . '.' . $file_extension;
        $target_path = $upload_dir . $new_filename;
        if(move_uploaded_file($file['tmp_name'], $target_path)){
          // Save mapping
          $map = get_profile_image_map($profile_map_file);
          $map[$adminid] = $new_filename;
          set_profile_image_map($profile_map_file, $map);
          add_activity_log($con, $adminid, 'Uploaded profile image');
          $_SESSION['profile_upload_success'] = true;
          header('Location: profile.php');
          exit();
        } else {
          echo "<script>alert('Error uploading file.');</script>";
        }
      }
    }
  } else {
    echo "<script>alert('No file uploaded or error occurred.');</script>";
  }
}
// Handle profile image remove
if(isset($_POST['remove_image'])){
  $adminid = intval($_SESSION['aid']);
  $map = get_profile_image_map($profile_map_file);
  if(isset($map[$adminid])){
    $file_to_remove = __DIR__ . '/uploads/profile_images/' . $map[$adminid];
    if(file_exists($file_to_remove)) unlink($file_to_remove);
    unset($map[$adminid]);
    set_profile_image_map($profile_map_file, $map);
    add_activity_log($con, $adminid, 'Removed profile image');
  }
  $_SESSION['profile_upload_success'] = true;
  header('Location: profile.php');
  exit();
}
// Handle delete logs
if(isset($_POST['delete_logs'])){
  $adminid = intval($_SESSION['aid']);
  mysqli_query($con, "DELETE FROM activity_logs WHERE admin_id='$adminid'");
  header('Location: profile.php');
  exit();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Boat Booking System  | My Profile</title>

  <!-- Google Font: Source Sans Pro -->
  <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
  <!-- Font Awesome -->
  <link rel="stylesheet" href="plugins/fontawesome-free/css/all.min.css">
  <!-- Theme style -->
  <link rel="stylesheet" href="dist/css/adminlte.min.css">
  <!--Function Email Availabilty---->
  <!-- SweetAlert2 -->
  <link rel="stylesheet" href="plugins/sweetalert2-theme-bootstrap-4/bootstrap-4.min.css">
  <script src="plugins/sweetalert2/sweetalert2.min.js"></script>

  <?php
  // Show success alert only once after upload with SweetAlert2
  if(isset($_SESSION['profile_upload_success'])){
    echo "<script>
      document.addEventListener('DOMContentLoaded', function() {
        Swal.fire({
          icon: 'success',
          title: 'Success',
          text: 'Profile image updated successfully',
          toast: true,
          position: 'top-end',
          showConfirmButton: false,
          timer: 3000
        });
      });
    </script>";
    unset($_SESSION['profile_upload_success']);
  }
  ?>
</head>
<body class="hold-transition sidebar-mini">
<div class="wrapper">
  <!-- Navbar -->
<?php include_once("includes/navbar.php");?>
  <!-- /.navbar -->

  <!-- Main Sidebar Container -->
 <?php include_once("includes/sidebar.php");?>

  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6">
            <h1>My Profile</h1>
          </div>
          <div class="col-sm-6">
            <!-- Breadcrumb removed as requested -->
          </div>
        </div>
      </div><!-- /.container-fluid -->
    </section>

    <!-- Simplified Profile Section -->
    <section class="content">
      <div class="container-fluid">
        <div class="row justify-content-center">
          <div class="col-md-8">
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">My Profile</h3>
              </div>
              <div class="card-body">
                <?php
                $adminid = intval($_SESSION['aid']);
                $query = mysqli_query($con, "SELECT * FROM admins WHERE admin_id='$adminid'");
                $result = mysqli_fetch_array($query);

                // Get profile image for this admin from persistent map
                $profile_img = 'dist/img/admindefault.png';
                $map = get_profile_image_map($profile_map_file);
                if(isset($map[$adminid]) && file_exists(__DIR__ . '/uploads/profile_images/' . $map[$adminid])){
                  $profile_img = 'uploads/profile_images/' . $map[$adminid];
                }
                ?>

                <div class="row">
                  <!-- Profile Image Column -->
                  <div class="col-md-4 text-center">
                    <img src="<?php echo $profile_img; ?>" class="img-fluid rounded-circle mb-3" style="width: 150px; height: 150px; object-fit: cover; border: 2px solid #007bff;" alt="Profile Image">

                    <div class="mb-3">
                      <form method="post" enctype="multipart/form-data" class="mb-2">
                        <div class="input-group">
                          <div class="custom-file">
                            <input type="file" class="custom-file-input" id="profileImage" name="profile_image" accept="image/*" required>
                            <label class="custom-file-label" for="profileImage">Choose file</label>
                          </div>
                        </div>
                        <button type="submit" name="upload_image" class="btn btn-primary btn-sm mt-2">Upload</button>
                      </form>

                      <?php if($profile_img !== 'dist/img/admindefault.png'): ?>
                        <form method="post">
                          <button type="submit" name="remove_image" class="btn btn-outline-danger btn-sm">Remove Photo</button>
                        </form>
                      <?php endif; ?>
                    </div>
                  </div>

                  <!-- Profile Info Column -->
                  <div class="col-md-8">
                    <form method="post">
                      <div class="form-group row">
                        <label for="firstname" class="col-sm-3 col-form-label">First Name</label>
                        <div class="col-sm-9">
                          <input type="text" class="form-control" id="firstname" name="firstname" value="<?php echo htmlspecialchars($result['first_name']); ?>" required>
                        </div>
                      </div>

                      <div class="form-group row">
                        <label for="lastname" class="col-sm-3 col-form-label">Last Name</label>
                        <div class="col-sm-9">
                          <input type="text" class="form-control" id="lastname" name="lastname" value="<?php echo htmlspecialchars($result['last_name']); ?>">
                        </div>
                      </div>

                      <div class="form-group row">
                        <label for="emailid" class="col-sm-3 col-form-label">Email</label>
                        <div class="col-sm-9">
                          <input type="email" class="form-control" id="emailid" name="emailid" value="<?php echo htmlspecialchars($result['email']); ?>" required>
                        </div>
                      </div>

                      <div class="form-group row">
                        <label for="mobilenumber" class="col-sm-3 col-form-label">Contact</label>
                        <div class="col-sm-9">
                          <input type="text" class="form-control" id="mobilenumber" name="mobilenumber" value="<?php echo htmlspecialchars($result['phone']); ?>" pattern="[0-9]{10,15}" title="Enter a valid contact number" required>
                        </div>
                      </div>

                      <div class="form-group row">
                        <label class="col-sm-3 col-form-label">Username</label>
                        <div class="col-sm-9">
                          <input type="text" class="form-control-plaintext" value="<?php echo htmlspecialchars($result['username']); ?>" readonly>
                        </div>
                      </div>

                      <div class="form-group row">
                        <label class="col-sm-3 col-form-label">Last Login</label>
                        <div class="col-sm-9">
                          <input type="text" class="form-control-plaintext" value="<?php echo htmlspecialchars($result['last_login']); ?>" readonly>
                        </div>
                      </div>

                      <div class="form-group row">
                        <div class="col-sm-9 offset-sm-3">
                          <button type="submit" class="btn btn-primary" name="update" id="update">Update Profile</button>
                          <a href="change-password.php" class="btn btn-outline-secondary ml-2">Change Password</a>
                        </div>
                      </div>
                    </form>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <?php
    // Handle update info POST
    if(isset($_POST['update'])){
      $fname = mysqli_real_escape_string($con, $_POST['firstname']);
      $lname = mysqli_real_escape_string($con, $_POST['lastname']);
      $email = mysqli_real_escape_string($con, $_POST['emailid']);
      $mobileno = mysqli_real_escape_string($con, $_POST['mobilenumber']);
      $adminid = intval($_SESSION['aid']);
      $query = mysqli_query($con, "UPDATE admins SET first_name='$fname', last_name='$lname', email='$email', phone='$mobileno' WHERE admin_id='$adminid'");
      if($query){
        // Log activity for name/email change
        add_activity_log($con, $adminid, 'Changed name and/or email');
        echo "<script>
          Swal.fire({
            icon: 'success',
            title: 'Success',
            text: 'Profile details updated successfully',
            showConfirmButton: false,
            timer: 1500
          }).then(function() {
            window.location = 'profile.php';
          });
        </script>";
        exit();
      } else {
        echo "<script>
          Swal.fire({
            icon: 'error',
            title: 'Error',
            text: 'Something went wrong. Please try again.'
          });
        </script>";
      }
    }
    ?>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php include_once('includes/footer.php');?>

</div>
<!-- ./wrapper -->

<!-- jQuery -->
<script src="plugins/jquery/jquery.min.js"></script>
<!-- Bootstrap 4 -->
<script src="plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
<!-- bs-custom-file-input -->
<script src="plugins/bs-custom-file-input/bs-custom-file-input.min.js"></script>
<!-- AdminLTE App -->
<script src="dist/js/adminlte.min.js"></script>
<!-- Page specific script -->
<script>
$(function () {
  bsCustomFileInput.init();
});
</script>
</body>
</html>
