/* Footer Styles */
.site-footer {
    position: relative;
    background: linear-gradient(to bottom, #1a1a1a, #000);
    color: #fff;
    padding: 80px 0 0;
    overflow: hidden;
    background-image: url('../../img/ramos.jpg');
    background-size: cover;
    background-position: center;
    width: 100%;
}

.site-footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.85);  /* Dark overlay for better text visibility */
    z-index: 1;
}

.footer-container {
    width: 100%;
    position: relative;
    z-index: 2;
}

.footer-content, .footer-bottom {
    position: relative;
    z-index: 2;  /* Place content above the overlay */
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 40px;
    margin-bottom: 50px;
    width: 100%;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
    padding: 0 15px;
}

.footer-brand {
    padding-right: 20px;
    text-align: left;
}

.footer-logos {
    display: flex;
    gap: 20px;
    margin-bottom: 40px;
    justify-content: flex-start;
}

.footer-logos img {
    height: 60px;
    width: auto;
    transition: transform 0.3s ease;
}

.footer-logos img:hover {
    transform: scale(1.05);
}

.footer-powered-by {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin: 0 0 40px 0;
}

.footer-powered-by span {
    color: #ffd700;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 15px;
    text-align: left;
}

.footer-powered-logo {
    height: 120px;
    width: auto;
    transition: transform 0.3s ease;
}

.footer-powered-logo:hover {
    transform: scale(1.1);
}

.footer-tagline {
    font-size: 1.1rem;
    color: #ccc;
    line-height: 1.6;
    margin-top: 20px;
    text-align: left;
}

.footer-links {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 30px;
    width: 100%;
}

.footer-column {
    padding-right: 20px;
}

.footer-column h4 {
    color: #fff;
    font-size: 1.2rem;
    margin-bottom: 25px;
    position: relative;
    padding-bottom: 10px;
}

.footer-column h4::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    width: 50px;
    height: 2px;
    background: linear-gradient(to right, #ffd700, transparent);
}

.footer-column ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-column ul li {
    margin-bottom: 20px;
}

.footer-column ul li a {
    color: #ccc;
    text-decoration: none;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
}

.footer-column ul li a:hover {
    color: #ffd700;
    transform: translateX(5px);
}

.footer-column ul li a i {
    font-size: 0.8rem;
    color: #ffd700;
}

.contact-info li {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    color: #ccc;
    margin-bottom: 20px;
}

.contact-info li i {
    color: #ffd700;
    margin-top: 5px;
    font-size: 1.1rem;
}

.contact-info li span {
    line-height: 1.4;
}

.social-links {
    display: flex;
    gap: 20px;
    margin-bottom: 30px;
}

.social-link {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    text-decoration: none;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.1);
    position: relative;
    z-index: 10;
    cursor: pointer;
    font-size: 1.2rem;
}

.social-link:hover {
    transform: translateY(-5px);
    background: #ffd700;
    color: #000;
}

.social-link.facebook:hover {
    background: #1877f2;
    color: #fff;
}

.social-link.instagram:hover {
    background: #e4405f;
    color: #fff;
}

.social-link.twitter:hover {
    background: #1da1f2;
    color: #fff;
}

.social-link.youtube:hover {
    background: #ff0000;
    color: #fff;
}

.footer-column p {
    color: #ccc;
    margin-bottom: 20px;
    font-size: 0.9rem;
    line-height: 1.5;
}

.newsletter-form {
    display: flex;
    gap: 10px;
    width: 100%;
    max-width: 350px;
    margin: 0;
}

.newsletter-form input {
    flex: 1;
    padding: 12px 15px;
    border: none;
    border-radius: 5px;
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    font-size: 0.9rem;
}

.newsletter-form input::placeholder {
    color: #ccc;
}

.newsletter-form button {
    padding: 12px 20px;
    border: none;
    border-radius: 5px;
    background: #ffd700;
    color: #000;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
}

.newsletter-form button:hover {
    background: #fff;
    transform: translateY(-2px);
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 20px 15px;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
    background: rgba(0, 0, 0, 0.3);
}

.copyright {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
}

.copyright p {
    color: #ccc;
    font-size: 0.9rem;
    margin: 0;
}

.footer-credits {
    display: flex;
    align-items: center;
    gap: 10px;
}

.footer-credits span {
    color: #ccc;
    font-size: 0.85rem;
}

.footer-credit-logo {
    height: 30px;
    width: auto;
    transition: transform 0.3s ease;
}

.footer-credit-logo:hover {
    transform: scale(1.1);
}

.footer-bottom-links {
    display: flex;
    gap: 20px;
}

.footer-bottom-links a {
    color: #ccc;
    text-decoration: none;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.footer-bottom-links a:hover {
    color: #ffd700;
}

/* Responsive Styles */
@media (max-width: 991px) {
    .footer-content {
        grid-template-columns: 1fr;
    }

    .footer-brand {
        padding-right: 0;
        text-align: left;
    }

    .footer-logos {
        justify-content: flex-start;
    }

    .footer-tagline {
        text-align: left;
    }
}

@media (max-width: 768px) {
    .footer-links {
        grid-template-columns: repeat(2, 1fr);
        gap: 40px 60px;
    }

    .footer-column {
        text-align: left;
        margin-bottom: 20px;
    }

    .footer-column h4::after {
        left: 0;
        transform: none;
    }

    .footer-column ul li a {
        justify-content: flex-start;
    }

    .contact-info li {
        justify-content: flex-start;
    }

    .social-links {
        justify-content: flex-start;
    }

    .footer-bottom {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }

    .footer-bottom-links {
        justify-content: center;
    }

    .copyright {
        align-items: center;
    }

    .footer-credits {
        margin-top: 5px;
    }

    .newsletter-form {
        max-width: 300px;
        margin: 0;
    }

    .footer-powered-by {
        align-items: flex-start;
    }
}

@media (max-width: 576px) {
    .site-footer {
        padding: 60px 0 0;
    }

    .footer-logos {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .footer-logos img {
        height: 50px;
    }

    .footer-powered-by span {
        font-size: 0.9rem;
    }

    .footer-powered-logo {
        height: 100px;
    }

    .footer-links {
        grid-template-columns: 1fr;
        gap: 50px;
    }

    .newsletter-form {
        flex-direction: column;
        margin: 0;
    }

    .newsletter-form button {
        width: 100%;
        margin-top: 10px;
    }

    .footer-column {
        margin-bottom: 20px;
        padding-right: 20px;
    }

    .contact-info li {
        flex-direction: row;
        align-items: flex-start;
        gap: 10px;
    }

    .contact-info li i {
        margin-top: 3px;
    }

    .footer-tagline {
        font-size: 1rem;
    }

    .footer-powered-by {
        margin: 15px 0;
    }
}
