<?php session_start();
error_reporting(0);
// Database Connection
include('includes/config.php');
//Validating Session
if(strlen($_SESSION['aid'])==0)
  {
header('location:../login/admin-login.php');
}
else{


  ?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Boat Booking System | B/w Dates Report Details</title>

  <!-- Google Font: Source Sans Pro -->
  <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
  <!-- Font Awesome -->
  <link rel="stylesheet" href="plugins/fontawesome-free/css/all.min.css">
  <!-- DataTables -->
  <link rel="stylesheet" href="plugins/datatables-bs4/css/dataTables.bootstrap4.min.css">
  <link rel="stylesheet" href="plugins/datatables-responsive/css/responsive.bootstrap4.min.css">
  <link rel="stylesheet" href="plugins/datatables-buttons/css/buttons.bootstrap4.min.css">
  <!-- Theme style -->
  <link rel="stylesheet" href="dist/css/adminlte.min.css">
</head>
<body class="hold-transition sidebar-mini">
<div class="wrapper">
  <!-- Navbar -->
<?php include_once("includes/navbar.php");?>
  <!-- /.navbar -->

 <?php include_once("includes/sidebar.php");?>

  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6">
            <?php $fdate=$_POST['fdate'];
            $tdate=$_POST['tdate'];

            $fromdate = date("d-m-Y", strtotime($fdate));
            $todate = date("d-m-Y", strtotime($tdate));

            ?>
            <h1>B/w Dates Report Details</h1>
          </div>
          <div class="col-sm-6">
            <ol class="breadcrumb float-sm-right">
              <li class="breadcrumb-item"><a href="dashboard.php">Home</a></li>
              <li class="breadcrumb-item active">B/w Dates Report Details</li>
            </ol>
          </div>
        </div>
      </div><!-- /.container-fluid -->
    </section>

    <!-- Main content -->
    <section class="content">
      <div class="container-fluid">
        <div class="row">
          <div class="col-12">
            <div class="card">


            <div class="card">
              <div class="card-header">
                <h3 class="card-title">Report Details</h3>
              </div>
              <!-- /.card-header -->
              <div class="card-body">
                <table id="example1" class="table table-bordered table-striped">
                  <thead>
                 <tr>
                    <th>#</th>
                    <th>Booking No</th>
                    <th>Customer ID</th>
                    <th>Email</th>
                    <th>Contact Number</th>
                    <th>No. of Pax</th>
                    <th>Booking Date/Time</th>
                    <th>Posting Date</th>
                    <th>Status</th>
                    <th>Action</th>
                  </tr>
                  </thead>
                  <tbody>
                  <?php
// Fetch bookings between dates from bookings table
$fdate = $_POST['fdate'];
$tdate = $_POST['tdate'];
$sql = "SELECT b.booking_id, b.customer_id, b.email, b.contact_number, b.no_of_pax, b.start_date, b.booking_time, b.created_at, b.booking_status
    FROM bookings b
    WHERE b.start_date BETWEEN ? AND ?
    ORDER BY b.start_date ASC";
$stmt = $con->prepare($sql);
$stmt->bind_param("ss", $fdate, $tdate);
$stmt->execute();
$query = $stmt->get_result();
if (!$query) {
    echo '<tr><td colspan=\'10\'>Error fetching bookings: ' . mysqli_error($con) . '</td></tr>';
} else if (mysqli_num_rows($query) == 0) {
    echo '<tr><td colspan=\'10\'>No bookings found for selected dates.</td></tr>';
} else {
    $cnt = 1;
    while ($result = mysqli_fetch_array($query)) {
        echo '<tr>';
        echo '<td>' . $cnt . '</td>';
        echo '<td>' . htmlspecialchars($result['booking_id']) . '</td>';
        echo '<td>' . (isset($result['customer_id']) ? htmlspecialchars($result['customer_id']) : 'N/A') . '</td>';
        echo '<td>' . (isset($result['email']) ? htmlspecialchars($result['email']) : 'N/A') . '</td>';
        echo '<td>' . (isset($result['contact_number']) ? htmlspecialchars($result['contact_number']) : 'N/A') . '</td>';
        echo '<td>' . (isset($result['no_of_pax']) ? htmlspecialchars($result['no_of_pax']) : 'N/A') . '</td>';
        echo '<td>' . (isset($result['start_date']) ? htmlspecialchars($result['start_date']) : 'N/A') . ' / ' . (isset($result['booking_time']) ? htmlspecialchars($result['booking_time']) : 'N/A') . '</td>';
        echo '<td>' . (isset($result['created_at']) ? htmlspecialchars($result['created_at']) : 'N/A') . '</td>';
        echo '<td>' . (isset($result['booking_status']) ? htmlspecialchars($result['booking_status']) : 'N/A') . '</td>';
        echo '<td><a href="booking-details.php?bid=' . urlencode($result['booking_id']) . '" title="View Details" class="btn btn-primary btn-sm">View Details</a></td>';
        echo '</tr>';
        $cnt++;
    }
}
?>

                </tbody>


                </table>
              </div>
              <!-- /.card-body -->
            </div>
            <!-- /.card -->
          </div>
          <!-- /.col -->
        </div>
        <!-- /.row -->
      </div>
      <!-- /.container-fluid -->
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php include_once('includes/footer.php');?>

  <!-- Control Sidebar -->
  <aside class="control-sidebar control-sidebar-dark">
    <!-- Control sidebar content goes here -->
  </aside>
  <!-- /.control-sidebar -->
</div>
<!-- ./wrapper -->

<!-- jQuery -->
<script src="plugins/jquery/jquery.min.js"></script>
<!-- Bootstrap 4 -->
<script src="plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
<!-- DataTables  & Plugins -->
<script src="plugins/datatables/jquery.dataTables.min.js"></script>
<script src="plugins/datatables-bs4/js/dataTables.bootstrap4.min.js"></script>
<script src="plugins/datatables-responsive/js/dataTables.responsive.min.js"></script>
<script src="plugins/datatables-responsive/js/responsive.bootstrap4.min.js"></script>
<script src="plugins/datatables-buttons/js/dataTables.buttons.min.js"></script>
<script src="plugins/datatables-buttons/js/buttons.bootstrap4.min.js"></script>
<script src="plugins/jszip/jszip.min.js"></script>
<script src="plugins/pdfmake/pdfmake.min.js"></script>
<script src="plugins/pdfmake/vfs_fonts.js"></script>
<script src="plugins/datatables-buttons/js/buttons.html5.min.js"></script>
<script src="plugins/datatables-buttons/js/buttons.print.min.js"></script>
<script src="plugins/datatables-buttons/js/buttons.colVis.min.js"></script>
<!-- AdminLTE App -->
<script src="dist/js/adminlte.min.js"></script>
<!-- AdminLTE for demo purposes -->
<script src="dist/js/demo.js"></script>
<!-- Page specific script -->
<script>
  $(function () {
    // Check if DataTable is already initialized
    if ($.fn.dataTable.isDataTable('#example1')) {
      // If already initialized, destroy it first
      $('#example1').DataTable().destroy();
    }

    // Initialize DataTable
    $("#example1").DataTable({
      "responsive": true,
      "lengthChange": false,
      "autoWidth": false,
      "buttons": ["copy", "csv", "excel", "pdf", "print", "colvis"]
    }).buttons().container().appendTo('#example1_wrapper .col-md-6:eq(0)');

    // Initialize example2 table if it exists
    if ($('#example2').length > 0) {
      if ($.fn.dataTable.isDataTable('#example2')) {
        $('#example2').DataTable().destroy();
      }
      $('#example2').DataTable({
        "paging": true,
        "lengthChange": false,
        "searching": false,
        "ordering": true,
        "info": true,
        "autoWidth": false,
        "responsive": true,
      });
    }
  });
</script>
</body>
</html>
<?php } ?>
