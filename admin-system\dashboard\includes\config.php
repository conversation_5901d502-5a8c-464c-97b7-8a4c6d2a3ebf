<?php
//time zone
date_default_timezone_set('Asia/Manila');

// Define database constants
define('DB_HOST', 'localhost');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_NAME', 'booking_system');

// Function to create database connection with error handling
function createDatabaseConnection() {
    try {
        // Set timeout to prevent long waits
        $options = [
            MYSQLI_OPT_CONNECT_TIMEOUT => 5
        ];

        // Create connection
        $connection = mysqli_init();

        // Set options
        foreach ($options as $option => $value) {
            $connection->options($option, $value);
        }

        // Connect to database
        $connection->real_connect(DB_HOST, DB_USER, DB_PASS, DB_NAME);

        // Check for connection errors
        if ($connection->connect_error) {
            throw new Exception("Connection failed: " . $connection->connect_error);
        }

        // Set character set
        $connection->set_charset("utf8");

        return $connection;
    } catch (Exception $e) {
        // Log the error
        error_log("Database connection error: " . $e->getMessage());

        // For admin pages, show a user-friendly error
        if (strpos($_SERVER['PHP_SELF'], 'admin') !== false) {
            echo "<div style='margin: 50px auto; max-width: 600px; padding: 20px; background-color: #f8d7da; border: 1px solid #f5c6cb; border-radius: 5px; color: #721c24;'>";
            echo "<h3>Database Connection Error</h3>";
            echo "<p>We're having trouble connecting to the database. This could be because:</p>";
            echo "<ul>";
            echo "<li>The MySQL server is not running</li>";
            echo "<li>The database credentials are incorrect</li>";
            echo "<li>The server is experiencing high load</li>";
            echo "</ul>";
            echo "<p>Please try the following:</p>";
            echo "<ul>";
            echo "<li>Refresh the page</li>";
            echo "<li>Check if MySQL service is running</li>";
            echo "<li>Contact the system administrator</li>";
            echo "</ul>";
            echo "</div>";
        }

        return false;
    }
}

// Create database connection
$con = createDatabaseConnection();

// Session start only if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Function to check if user is logged in
function checkLogin() {
    if (!isset($_SESSION['aid']) || empty($_SESSION['aid'])) {
        header("Location: ../../login/admin-login.php");
        exit();
    }
}

// Function to get admin details
function getAdminDetails($admin_id) {
    global $con;

    // Check if connection is valid
    if (!$con) {
        return false;
    }

    try {
        $stmt = $con->prepare("SELECT * FROM admins WHERE admin_id = ?");
        if (!$stmt) {
            return false;
        }

        $stmt->bind_param("i", $admin_id);
        $stmt->execute();
        $result = $stmt->get_result();
        return $result->fetch_assoc();
    } catch (Exception $e) {
        error_log("Error getting admin details: " . $e->getMessage());
        return false;
    }
}

?>
