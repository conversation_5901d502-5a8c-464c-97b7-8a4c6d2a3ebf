# Project Structure Documentation

## Recommended Directory Structure

```
Home System/
├── Interface/
│   ├── config/                 # All configuration files
│   │   ├── database.php       # Database connection
│   │   └── config.php         # General configuration
│   │
│   ├── assets/                # All static assets
│   │   ├── css/              # Stylesheets
│   │   ├── js/               # JavaScript files
│   │   └── img/              # Images
│   │
│   ├── includes/             # Reusable PHP components
│   │   ├── header.php
│   │   └── footer.php
│   │
│   ├── classes/              # PHP classes
│   │   ├── Auth.php
│   │   ├── Security.php
│   │   └── Database.php
│   │
│   ├── pages/               # Main application pages
│   │   ├── admin/          # Admin pages
│   │   └── user/           # User pages
│   │
│   └── process/            # Processing scripts
│       ├── booking/
│       └── auth/
│
└── sql/                    # Database scripts
    └── create_tables.php
```

## Files to Move

1. Configuration Files:
   - Move `Home System/Interface/php/config/connect.php` to `Home System/Interface/config/database.php`
   - Move any other config files to `Home System/Interface/config/`

2. Static Assets:
   - Move `Home System/Interface/css/` to `Home System/Interface/assets/css/`
   - Move `Home System/Interface/js/` to `Home System/Interface/assets/js/`
   - Move `Home System/Interface/img/` to `Home System/Interface/assets/img/`

3. PHP Classes:
   - Keep `Home System/Interface/classes/` as is
   - Ensure all class files are in this directory

4. Pages:
   - Move admin dashboard files to `Home System/Interface/pages/admin/`
   - Move user dashboard files to `Home System/Interface/pages/user/`

5. Process Files:
   - Move booking-related process files to `Home System/Interface/process/booking/`
   - Move authentication-related process files to `Home System/Interface/process/auth/`

## Important Notes

1. After moving files, update all include/require paths in PHP files
2. Update any asset references in HTML/CSS/JS files
3. Test the application after each major move
4. Keep backups of files before moving them

## Next Steps

1. Create the new directory structure
2. Move files one section at a time
3. Update file references
4. Test functionality
5. Remove old directories once everything is working 