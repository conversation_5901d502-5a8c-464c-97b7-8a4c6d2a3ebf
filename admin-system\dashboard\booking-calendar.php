<?php
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Database Connection
include('includes/config.php');

// Test database connection
if (!$con) {
    die("Database connection failed: " . mysqli_connect_error());
}

// Validating Session
if (!isset($_SESSION['aid']) || strlen($_SESSION['aid']) == 0) {
    header('location:../login/admin-login.php');
    exit();
}

// Get current month and year
$month = isset($_GET['month']) ? intval($_GET['month']) : intval(date('m'));
$year = isset($_GET['year']) ? intval($_GET['year']) : intval(date('Y'));

// Validate month and year
if ($month < 1 || $month > 12) {
    $month = date('m');
}
if ($year < 2020 || $year > 2030) {
    $year = date('Y');
}

// Calculate previous and next month
$prevMonth = $month - 1;
$prevYear = $year;
if ($prevMonth < 1) {
    $prevMonth = 12;
    $prevYear--;
}

$nextMonth = $month + 1;
$nextYear = $year;
if ($nextMonth > 12) {
    $nextMonth = 1;
    $nextYear++;
}

// Get first day of the month
$firstDayOfMonth = mktime(0, 0, 0, $month, 1, $year);
$numberDays = date('t', $firstDayOfMonth);
$dateComponents = getdate($firstDayOfMonth);
$monthName = $dateComponents['month'];
$dayOfWeek = $dateComponents['wday'];

// Get bookings for the current month
$startDate = "$year-$month-01";
$endDate = "$year-$month-$numberDays";

$stmt = $con->prepare("SELECT
                      b.booking_id,
                      COALESCE(b.booking_code, 'No Code') AS booking_code,
                      b.start_date,
                      b.end_date,
                      b.booking_status,
                      COALESCE(CONCAT(COALESCE(c.first_name, ''), ' ', COALESCE(c.last_name, '')),
                               CONCAT(COALESCE(b.first_name, ''), ' ', COALESCE(b.last_name, '')),
                               'Unknown') AS customer_name,
                      COALESCE(bt.name, 'Unknown Boat') AS boat_name
                      FROM bookings b
                      LEFT JOIN customers c ON b.customer_id = c.customer_id
                      LEFT JOIN boats bt ON b.boat_id = bt.boat_id
                      WHERE (DATE(b.start_date) BETWEEN ? AND ?) OR (DATE(b.end_date) BETWEEN ? AND ?)
                      ORDER BY b.start_date");
$stmt->bind_param("ssss", $startDate, $endDate, $startDate, $endDate);
$stmt->execute();
$result = $stmt->get_result();

$bookings = [];
while ($row = $result->fetch_assoc()) {
    $startDateObj = new DateTime($row['start_date']);
    $endDateObj = new DateTime($row['end_date']);

    // Format dates for display
    $startDay = $startDateObj->format('j');
    $endDay = $endDateObj->format('j');

    // Only include if the booking is in the current month
    $startMonth = $startDateObj->format('n');
    $endMonth = $endDateObj->format('n');
    $startYear = $startDateObj->format('Y');
    $endYear = $endDateObj->format('Y');

    // Add booking to the array for each day it spans
    $currentDate = clone $startDateObj;
    while ($currentDate <= $endDateObj) {
        $currentDay = $currentDate->format('j');
        $currentMonth = $currentDate->format('n');
        $currentYear = $currentDate->format('Y');

        // Only add if the date is in the current month/year we're viewing
        if ($currentMonth == $month && $currentYear == $year) {
            if (!isset($bookings[$currentDay])) {
                $bookings[$currentDay] = [];
            }
            $bookings[$currentDay][] = [
                'id' => $row['booking_id'],
                'code' => $row['booking_code'],
                'customer' => $row['customer_name'],
                'boat' => $row['boat_name'],
                'status' => $row['booking_status'],
                'start_date' => $row['start_date'],
                'end_date' => $row['end_date'],
                'is_start' => ($currentDay == $startDay && $currentMonth == $startMonth && $currentYear == $startYear),
                'is_end' => ($currentDay == $endDay && $currentMonth == $endMonth && $currentYear == $endYear)
            ];
        }

        // Move to next day
        $currentDate->modify('+1 day');
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Booking Calendar | Online Booking System</title>

  <!-- Google Font: Source Sans Pro -->
  <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
  <!-- Font Awesome -->
  <link rel="stylesheet" href="plugins/fontawesome-free/css/all.min.css">
  <!-- Theme style -->
  <link rel="stylesheet" href="dist/css/adminlte.min.css">
  <!-- FullCalendar CSS -->
  <link rel="stylesheet" href="plugins/fullcalendar/main.min.css">
  <!-- Sidebar fix -->
  <link rel="stylesheet" href="css/sidebar-fix.css">
  <!-- Custom CSS for Calendar -->
  <style>
    .calendar-day {
      height: 120px;
      border: 1px solid #ddd;
      vertical-align: top;
      width: 14.28%;
      position: relative;
      overflow: hidden;
    }
    .calendar-day-np {
      background: #f5f5f5;
    }
    .date-number {
      font-weight: bold;
      padding: 5px;
      color: #333;
      font-size: 14px;
    }
    .calendar-day:hover {
      background-color: #f8f9fa;
    }
    .booking-item {
      margin: 2px;
      padding: 2px 5px;
      border-radius: 3px;
      font-size: 11px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      cursor: pointer;
    }
    .booking-pending {
      background-color: #ffc107;
      border-left: 3px solid #e0a800;
    }
    .booking-confirmed {
      background-color: #28a745;
      border-left: 3px solid #218838;
      color: white;
    }
    .booking-cancelled {
      background-color: #dc3545;
      border-left: 3px solid #c82333;
      color: white;
    }
    .booking-start {
      border-left-width: 5px;
    }
    .booking-end {
      border-right-width: 5px;
    }
    .month-nav {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
    }
    .today-btn {
      margin: 0 10px;
    }
    /* Remove all scrollbars */
    .calendar-container {
      overflow: auto;
      max-width: 100%;
      margin-bottom: 10px;
    }
    .table-responsive {
      overflow-x: visible;
    }
    @media (max-width: 768px) {
      .calendar-day {
        height: 80px;
      }
      .booking-item {
        font-size: 9px;
      }
    }
  </style>
</head>
<body class="hold-transition sidebar-mini">
<div class="wrapper">
  <!-- Navbar -->
  <?php include_once("includes/navbar.php");?>
  <!-- /.navbar -->

  <!-- Main Sidebar Container -->
  <?php include_once("includes/sidebar.php");?>

  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6">
            <h1>Booking Calendar</h1>
          </div>
          <div class="col-sm-6">
            <ol class="breadcrumb float-sm-right">
              <li class="breadcrumb-item"><a href="dashboard.php">Home</a></li>
              <li class="breadcrumb-item active">Booking Calendar</li>
            </ol>
          </div>
        </div>
      </div><!-- /.container-fluid -->
    </section>

    <!-- Main content -->
    <section class="content">
      <div class="container-fluid">
        <div class="row">
          <div class="col-12">
            <div class="card">
              <div class="card-header">
                <div class="month-nav">
                  <a href="?month=<?php echo $prevMonth; ?>&year=<?php echo $prevYear; ?>" class="btn btn-default">
                    <i class="fas fa-chevron-left"></i> Previous Month
                  </a>
                  <h3 class="card-title"><?php echo $monthName . ' ' . $year; ?></h3>
                  <div>
                    <a href="?month=<?php echo date('m'); ?>&year=<?php echo date('Y'); ?>" class="btn btn-info today-btn">
                      Today
                    </a>
                    <a href="?month=<?php echo $nextMonth; ?>&year=<?php echo $nextYear; ?>" class="btn btn-default">
                      Next Month <i class="fas fa-chevron-right"></i>
                    </a>
                  </div>
                </div>
              </div>
              <!-- /.card-header -->
              <div class="card-body">
                <div class="calendar-container">
                  <table class="table table-bordered" style="width: 100%; table-layout: fixed;">
                    <thead>
                      <tr>
                        <th>Sunday</th>
                        <th>Monday</th>
                        <th>Tuesday</th>
                        <th>Wednesday</th>
                        <th>Thursday</th>
                        <th>Friday</th>
                        <th>Saturday</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <?php
                        // Create calendar
                        $dayCount = 1;

                        // Add empty cells for days before the first day of the month
                        for ($i = 0; $i < $dayOfWeek; $i++) {
                            echo '<td class="calendar-day calendar-day-np"></td>';
                        }

                        // Add days of the month
                        for ($day = 1; $day <= $numberDays; $day++) {
                            $today = ($day == date('j') && $month == date('m') && $year == date('Y')) ? 'bg-light' : '';

                            echo '<td class="calendar-day ' . $today . '">';
                            echo '<div class="date-number">' . $day . '</div>';

                            // Display bookings for this day
                            if (isset($bookings[$day])) {
                                foreach ($bookings[$day] as $booking) {
                                    $statusClass = '';
                                    switch ($booking['status']) {
                                        case 'pending':
                                            $statusClass = 'booking-pending';
                                            break;
                                        case 'confirmed':
                                        case 'accepted':
                                            $statusClass = 'booking-confirmed';
                                            break;
                                        case 'cancelled':
                                        case 'rejected':
                                            $statusClass = 'booking-cancelled';
                                            break;
                                        default:
                                            $statusClass = 'booking-pending';
                                    }

                                    // Add start/end indicators
                                    if ($booking['is_start']) {
                                        $statusClass .= ' booking-start';
                                    }
                                    if ($booking['is_end']) {
                                        $statusClass .= ' booking-end';
                                    }

                                    // Add null checks to prevent deprecated warnings
                                    $customerName = isset($booking['customer']) && $booking['customer'] !== null ? $booking['customer'] : 'Unknown';
                                    $boatName = isset($booking['boat']) && $booking['boat'] !== null ? $booking['boat'] : 'Unknown';

                                    echo '<div class="booking-item ' . $statusClass . '"
                                            data-toggle="tooltip"
                                            title="' . htmlspecialchars($customerName) . ' - ' . htmlspecialchars($boatName) . '"
                                            onclick="viewBooking(' . $booking['id'] . ')">';
                                    $bookingCode = isset($booking['code']) && $booking['code'] !== null ? $booking['code'] : 'No Code';
                                    echo htmlspecialchars($bookingCode);
                                    echo '</div>';
                                }
                            }

                            echo '</td>';

                            // Start a new row if it's the end of the week
                            if (($dayOfWeek + $day) % 7 == 0) {
                                echo '</tr><tr>';
                            }
                        }

                        // Add empty cells for days after the last day of the month
                        $remainingCells = 7 - (($dayOfWeek + $numberDays) % 7);
                        if ($remainingCells < 7) {
                            for ($i = 0; $i < $remainingCells; $i++) {
                                echo '<td class="calendar-day calendar-day-np"></td>';
                            }
                        }
                        ?>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
              <!-- /.card-body -->
              <div class="card-footer">
                <div class="row">
                  <div class="col-md-4">
                    <div class="d-flex align-items-center mb-2">
                      <div class="mr-2" style="width:20px;height:10px;background-color:#ffc107;"></div>
                      <span>Pending Bookings</span>
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="d-flex align-items-center mb-2">
                      <div class="mr-2" style="width:20px;height:10px;background-color:#28a745;"></div>
                      <span>Confirmed Bookings</span>
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="d-flex align-items-center mb-2">
                      <div class="mr-2" style="width:20px;height:10px;background-color:#dc3545;"></div>
                      <span>Cancelled Bookings</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!-- /.card -->
          </div>
          <!-- /.col -->
        </div>
        <!-- /.row -->
      </div>
      <!-- /.container-fluid -->
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->

  <!-- Booking Details Modal -->
  <div class="modal fade" id="bookingDetailsModal">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header bg-info">
          <h4 class="modal-title">Booking Details</h4>
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
          <div class="text-center py-4">
            <i class="fas fa-spinner fa-spin fa-3x"></i>
            <p class="mt-2">Loading booking details...</p>
          </div>
          <div id="bookingDetails" style="display: none;">
            <!-- Booking details will be loaded here -->
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
          <a href="#" id="viewFullDetailsBtn" class="btn btn-primary">View Full Details</a>
        </div>
      </div>
      <!-- /.modal-content -->
    </div>
    <!-- /.modal-dialog -->
  </div>
  <!-- /.modal -->

  <?php include_once('includes/footer.php');?>
</div>
<!-- ./wrapper -->

<!-- jQuery -->
<script src="plugins/jquery/jquery.min.js"></script>
<!-- Bootstrap 4 -->
<script src="plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
<!-- AdminLTE App -->
<script src="dist/js/adminlte.min.js"></script>
<!-- Page specific script -->
<script>
  $(function () {
    // Enable tooltips
    $('[data-toggle="tooltip"]').tooltip();
  });

  // Function to view booking details
  function viewBooking(bookingId) {
    // Show modal with loading spinner
    $('#bookingDetailsModal').modal('show');
    $('#bookingDetails').hide();

    // Set the view full details button URL
    $('#viewFullDetailsBtn').attr('href', 'view-booking.php?id=' + bookingId);

    // Fetch booking details via AJAX
    $.ajax({
      url: 'get-booking-details.php',
      type: 'POST',
      data: { id: bookingId },
      dataType: 'json',
      success: function(response) {
        if (response.success) {
          // Format the booking details HTML
          var statusClass = '';
          var statusText = '';

          switch (response.data.booking_status) {
            case 'pending':
              statusClass = 'warning';
              statusText = 'PENDING';
              break;
            case 'confirmed':
            case 'accepted':
              statusClass = 'success';
              statusText = 'CONFIRMED';
              break;
            case 'cancelled':
            case 'rejected':
              statusClass = 'danger';
              statusText = 'CANCELLED';
              break;
            default:
              statusClass = 'secondary';
              statusText = response.data.booking_status.toUpperCase();
          }

          // Format dates
          var startDate = new Date(response.data.start_date);
          var endDate = new Date(response.data.end_date);
          var formattedStartDate = startDate.toLocaleDateString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
          });
          var formattedEndDate = endDate.toLocaleDateString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
          });

          // Build HTML
          var html = `
            <div class="row">
              <div class="col-md-6">
                <h5>Booking Information</h5>
                <table class="table table-bordered">
                  <tr>
                    <th>Booking Code</th>
                    <td>${response.data.booking_code}</td>
                  </tr>
                  <tr>
                    <th>Status</th>
                    <td><span class="badge badge-${statusClass}">${statusText}</span></td>
                  </tr>
                  <tr>
                    <th>Start Date</th>
                    <td>${formattedStartDate}</td>
                  </tr>
                  <tr>
                    <th>End Date</th>
                    <td>${formattedEndDate}</td>
                  </tr>
                  <tr>
                    <th>Payment Method</th>
                    <td>${response.data.payment_method ? response.data.payment_method.toUpperCase() : 'N/A'}</td>
                  </tr>
                  <tr>
                    <th>Total Amount</th>
                    <td>₱${parseFloat(response.data.total).toFixed(2)}</td>
                  </tr>
                </table>
              </div>
              <div class="col-md-6">
                <h5>Customer Information</h5>
                <table class="table table-bordered">
                  <tr>
                    <th>Name</th>
                    <td>${response.data.first_name} ${response.data.last_name}</td>
                  </tr>
                  <tr>
                    <th>Email</th>
                    <td>${response.data.email || 'N/A'}</td>
                  </tr>
                  <tr>
                    <th>Phone</th>
                    <td>${response.data.contact_number || 'N/A'}</td>
                  </tr>
                  <tr>
                    <th>Boat</th>
                    <td>${response.data.boat_name || 'N/A'}</td>
                  </tr>
                  <tr>
                    <th>Number of Passengers</th>
                    <td>${response.data.no_of_pax || 'N/A'}</td>
                  </tr>
                </table>
              </div>
            </div>
          `;

          // Update the modal content
          $('#bookingDetails').html(html).show();
        } else {
          $('#bookingDetails').html('<div class="alert alert-danger">Error loading booking details.</div>').show();
        }
      },
      error: function() {
        $('#bookingDetails').html('<div class="alert alert-danger">Error loading booking details. Please try again.</div>').show();
      }
    });
  }
</script>
</body>
</html>
