<?php
// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Database Connection
include('includes/config.php');

// Check if the security.php file exists at the specified path
$security_path = "../../../php/utils/security.php";
if (file_exists($security_path)) {
    require_once($security_path);
} else {
    // If the file doesn't exist, define a basic safe_post function
    if (!function_exists('safe_post')) {
        function safe_post($key, $default = '') {
            return isset($_POST[$key]) ? htmlspecialchars(trim($_POST[$key]), ENT_QUOTES, 'UTF-8') : $default;
        }
    }
}

// Validating Session
if(!isset($_SESSION['aid']) || strlen($_SESSION['aid'])==0) {
    header('location:../login/admin-login.php');
    exit();
} else {
    //Code For Deletion the boats - Legacy method, now using AJAX
    if(isset($_GET['action']) && $_GET['action']=='delete'){
        $bid=intval($_GET['bid']);

        // Check if boat is being used in any boat_reservations
        $checkStmt = $con->prepare("SELECT COUNT(*) as count FROM boat_reservations WHERE boat_id=?");
        $checkStmt->bind_param('i', $bid);
        $checkStmt->execute();
        $checkResult = $checkStmt->get_result()->fetch_assoc();
        $checkStmt->close();

        if($checkResult['count'] > 0) {
            echo "<script>alert('Cannot delete boat as it is being used in bookings.');</script>";
        } else {
            $deleteStmt = $con->prepare("DELETE FROM boats WHERE boat_id=?");
            $deleteStmt->bind_param('i', $bid);
            if($deleteStmt->execute()){
                $deleteStmt->close();
                echo "<script>alert('Boat details deleted successfully.');</script>";
                echo "<script type='text/javascript'> document.location = 'manage-boat.php'; </script>";
            } else {
                $deleteStmt->close();
                echo "<script>alert('Something went wrong. Please try again.');</script>";
            }
        }
    }
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Boat Booking System | Manage Boat</title>

  <!-- Google Font: Source Sans Pro -->
  <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
  <!-- Font Awesome -->
  <link rel="stylesheet" href="plugins/fontawesome-free/css/all.min.css">
  <!-- DataTables -->
  <link rel="stylesheet" href="plugins/datatables-bs4/css/dataTables.bootstrap4.min.css">
  <link rel="stylesheet" href="plugins/datatables-responsive/css/responsive.bootstrap4.min.css">
  <link rel="stylesheet" href="plugins/datatables-buttons/css/buttons.bootstrap4.min.css">
  <!-- SweetAlert2 -->
  <link rel="stylesheet" href="plugins/sweetalert2-theme-bootstrap-4/bootstrap-4.min.css">
  <!-- Theme style -->
  <link rel="stylesheet" href="dist/css/adminlte.min.css">
  <style>
    .availability-calendar {
      width: 100%;
      border-collapse: collapse;
    }
    .availability-calendar th, .availability-calendar td {
      border: 1px solid #ddd;
      padding: 8px;
      text-align: center;
    }
    .availability-calendar th {
      background-color: #f2f2f2;
    }
    .availability-status-available {
      background-color: #d4edda;
      color: #155724;
    }
    .availability-status-not-available {
      background-color: #f8d7da;
      color: #721c24;
    }
    .availability-status-maintenance {
      background-color: #fff3cd;
      color: #856404;
    }
    .date-header {
      font-size: 0.8rem;
      font-weight: bold;
    }
    .nav-tabs-custom .nav-item .nav-link.active {
      font-weight: bold;
      border-top: 3px solid #007bff;
    }
  </style>
</head>
<body class="hold-transition sidebar-mini">
<div class="wrapper">
  <!-- Navbar -->
  <?php include_once("includes/navbar.php");?>
  <!-- /.navbar -->

  <?php include_once("includes/sidebar.php");?>

  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6">
            <h1>Manage Boats</h1>
          </div>
          <div class="col-sm-6">
            <button type="button" class="btn btn-success float-right ml-2" id="addNewBoatBtn">
              <i class="fas fa-plus"></i> Add New Boat
            </button>
            <button type="button" class="btn btn-primary float-right" id="markAvailabilityBtn">
              <i class="fas fa-calendar-check"></i> Mark Availability
            </button>
          </div>
        </div>
      </div>
    </section>

    <!-- Main content -->
    <section class="content">
      <div class="container-fluid">
        <!-- Tabs -->
        <?php
        // Check if there's an active tab in the URL
        $activeTab = isset($_GET['tab']) ? $_GET['tab'] : 'boat-list';
        $boatListActive = ($activeTab == 'boat-list') ? 'active' : '';
        $calendarActive = ($activeTab == 'calendar') ? 'active' : '';
        $masterlistActive = ($activeTab == 'masterlist') ? 'active' : '';
        $boatListShow = ($activeTab == 'boat-list') ? 'show active' : '';
        $calendarShow = ($activeTab == 'calendar') ? 'show active' : '';
        $masterlistShow = ($activeTab == 'masterlist') ? 'show active' : '';
        ?>
        <ul class="nav nav-tabs nav-tabs-custom" id="boatTabs" role="tablist">
          <li class="nav-item">
            <a class="nav-link <?php echo $boatListActive; ?>" id="boat-list-tab" data-toggle="tab" href="#boat-list" role="tab" aria-controls="boat-list" aria-selected="<?php echo ($activeTab == 'boat-list') ? 'true' : 'false'; ?>">
              <i class="fas fa-ship"></i> Boat List
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link <?php echo $calendarActive; ?>" id="availability-calendar-tab" data-toggle="tab" href="#availability-calendar" role="tab" aria-controls="availability-calendar" aria-selected="<?php echo ($activeTab == 'calendar') ? 'true' : 'false'; ?>">
              <i class="fas fa-calendar-alt"></i> Availability Calendar
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link <?php echo ($activeTab == 'masterlist') ? 'active' : ''; ?>" id="masterlist-tab" data-toggle="tab" href="#masterlist" role="tab" aria-controls="masterlist" aria-selected="<?php echo ($activeTab == 'masterlist') ? 'true' : 'false'; ?>">
              <i class="fas fa-clipboard-list"></i> Boat Masterlist
            </a>
          </li>
        </ul>

        <div class="tab-content mt-3" id="boatTabsContent">
          <!-- Boat List Tab -->
          <div class="tab-pane fade <?php echo $boatListShow; ?>" id="boat-list" role="tabpanel" aria-labelledby="boat-list-tab">
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">Boat List</h3>
              </div>
              <div class="card-body">
                <table id="boatTable" class="table table-bordered table-striped">
                  <thead>
                  <tr>
                    <th>#</th>
                    <th>Name</th>
                    <th>Type</th>
                    <th>Capacity</th>
                    <th>Price Per Day</th>
                    <th>Status</th>
                    <th>Assigned by Tourism Office</th>
                    <th>Actions</th>
                  </tr>
                  </thead>
                  <tbody>
                  <?php
                  $ret = mysqli_query($con, "SELECT * FROM boats ORDER BY boat_id");
                  $cnt = 1;
                  while($row = mysqli_fetch_assoc($ret)) {
                    $status = $row['status'] ?? 'Available';
                    $statusClass = '';
                    switch($status) {
                      case 'Available':
                        $statusClass = 'success';
                        break;
                      case 'Maintenance':
                        $statusClass = 'warning';
                        break;
                      case 'Not Available':
                        $statusClass = 'danger';
                        break;
                    }

                    // Format boat type for display
                    $type = $row['type'] ?? 'small';
                    $typeDisplay = ucfirst($type) . ' Boat';

                    // Check if the boat is assigned by tourism office
                    $assignedByTourismOffice = isset($row['assigned_by_tourism_office']) && $row['assigned_by_tourism_office'] == 1 ? 'Yes' : 'No';
                    $assignedClass = $assignedByTourismOffice == 'Yes' ? 'success' : 'secondary';

                    echo '<tr>
                      <td>'.$cnt++.'</td>
                      <td>'.htmlspecialchars($row['name']).'</td>
                      <td>'.$typeDisplay.'</td>
                      <td>'.intval($row['capacity'] ?? 1).' persons</td>
                      <td>P '.number_format($row['price_per_day'], 2).'</td>
                      <td><span class="badge badge-'.$statusClass.'">'.$status.'</span></td>
                      <td><span class="badge badge-'.$assignedClass.'">'.$assignedByTourismOffice.'</span></td>
                      <td>
                        <button type="button" class="btn btn-primary btn-sm" onclick="editBoat('.$row['boat_id'].')">
                          <i class="fas fa-edit"></i>
                        </button>
                        <button type="button" class="btn btn-danger btn-sm" onclick="deleteBoat('.$row['boat_id'].')">
                          <i class="fas fa-trash"></i>
                        </button>
                      </td>
                    </tr>';
                  }
                  ?>
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <!-- Availability Calendar Tab -->
          <div class="tab-pane fade <?php echo $calendarShow; ?>" id="availability-calendar" role="tabpanel" aria-labelledby="availability-calendar-tab">
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">Boat Availability Calendar</h3>
                <div class="card-tools">
                  <button type="button" class="btn btn-primary btn-sm" id="refreshCalendarBtn">
                    <i class="fas fa-sync-alt"></i> Refresh
                  </button>
                </div>
              </div>
              <div class="card-body">
                <!-- Calendar View -->
                <div class="table-responsive">
                  <table class="table table-bordered availability-calendar">
                    <thead>
                      <tr>
                        <th>Boat</th>
                        <?php
                        // Generate column headers for the next 14 days
                        $currentDate = new DateTime();
                        for($i = 0; $i < 14; $i++) {
                            $date = clone $currentDate;
                            $date->modify("+$i day");
                            echo '<th class="date-header">' . $date->format('D') . '<br>' . $date->format('M j') . '</th>';
                        }
                        ?>
                      </tr>
                    </thead>
                    <tbody id="availabilityCalendarBody">
                      <?php
                      // Create boat_availability_dates table if it doesn't exist
                      $con->query("CREATE TABLE IF NOT EXISTS `boat_availability_dates` (
                          `id` int(11) NOT NULL AUTO_INCREMENT,
                          `boat_id` int(11) NOT NULL,
                          `available_date` date NOT NULL,
                          `status` enum('available','not available','maintenance') NOT NULL DEFAULT 'available',
                          `notes` text DEFAULT NULL,
                          `added_by` int(11) DEFAULT NULL,
                          `created_at` datetime NOT NULL DEFAULT current_timestamp(),
                          `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
                          PRIMARY KEY (`id`),
                          UNIQUE KEY `boat_date` (`boat_id`,`available_date`),
                          KEY `boat_id` (`boat_id`),
                          KEY `available_date` (`available_date`),
                          KEY `added_by` (`added_by`)
                      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci");

                      // Get all boats
                      $boatsQuery = $con->query("SELECT * FROM boats ORDER BY name");

                      // Get availability data for the next 14 days
                      $startDate = date('Y-m-d');
                      $endDate = date('Y-m-d', strtotime('+14 days'));

                      $availabilityData = [];
                      $availQuery = $con->prepare("
                          SELECT
                              bad.id,
                              bad.boat_id,
                              bad.available_date,
                              bad.status,
                              bad.notes
                          FROM
                              boat_availability_dates bad
                          WHERE
                              bad.available_date BETWEEN ? AND ?
                      ");
                      $availQuery->bind_param("ss", $startDate, $endDate);
                      $availQuery->execute();
                      $availResult = $availQuery->get_result();

                      while($row = $availResult->fetch_assoc()) {
                          $boatId = $row['boat_id'];
                          $date = $row['available_date'];
                          $availabilityData[$boatId][$date] = [
                              'status' => $row['status'],
                              'notes' => $row['notes']
                          ];
                      }

                      // Generate calendar rows
                      while($boat = $boatsQuery->fetch_assoc()) {
                          echo '<tr>';
                          echo '<td>' . htmlspecialchars($boat['name']) . '</td>';

                          // Generate cells for each date
                          $currentDate = new DateTime();
                          for($i = 0; $i < 14; $i++) {
                              $date = clone $currentDate;
                              $date->modify("+$i day");
                              $dateStr = $date->format('Y-m-d');

                              $boatId = $boat['boat_id'];
                              $statusClass = '';
                              $statusText = '';
                              $notes = '';

                              // Check if we have specific availability for this date
                              if(isset($availabilityData[$boatId][$dateStr])) {
                                  $dateInfo = $availabilityData[$boatId][$dateStr];
                                  $status = $dateInfo['status'];
                                  $notes = $dateInfo['notes'];

                                  switch($status) {
                                      case 'available':
                                          $statusClass = 'availability-status-available';
                                          $statusText = 'Available';
                                          break;
                                      case 'not available':
                                          $statusClass = 'availability-status-not-available';
                                          $statusText = 'Not Available';
                                          break;
                                      case 'maintenance':
                                          $statusClass = 'availability-status-maintenance';
                                          $statusText = 'Maintenance';
                                          break;
                                  }
                              } else {
                                  // Use default status from boats table
                                  $status = strtolower($boat['status'] ?? 'Available');

                                  switch($status) {
                                      case 'available':
                                          $statusClass = 'availability-status-available';
                                          $statusText = 'Available';
                                          break;
                                      case 'not available':
                                          $statusClass = 'availability-status-not-available';
                                          $statusText = 'Not Available';
                                          break;
                                      case 'maintenance':
                                          $statusClass = 'availability-status-maintenance';
                                          $statusText = 'Maintenance';
                                          break;
                                      default:
                                          $statusClass = '';
                                          $statusText = 'Unknown';
                                  }
                              }

                              $title = "Status: $statusText";
                              if(!empty($notes)) {
                                  $title .= "\nNotes: $notes";
                              }

                              echo '<td class="' . $statusClass . '" title="' . htmlspecialchars($title) . '"
                                  data-boat-id="' . $boatId . '"
                                  data-date="' . $dateStr . '"
                                  data-status="' . $status . '"
                                  data-notes="' . htmlspecialchars($notes) . '"
                                  onclick="markBoatAvailability(' . $boatId . ', \'' . $dateStr . '\', \'' . $status . '\', \'' . htmlspecialchars($notes) . '\')"
                                  style="cursor: pointer;">' . $statusText . '</td>';
                          }

                          echo '</tr>';
                      }
                      ?>
                    </tbody>
                  </table>
                </div>

                <!-- Availability Legend -->
                <div class="mt-3">
                  <h5>Legend:</h5>
                  <div class="d-flex">
                    <div class="mr-4">
                      <span class="badge availability-status-available">Available</span> - Boat is available for booking
                    </div>
                    <div class="mr-4">
                      <span class="badge availability-status-not-available">Not Available</span> - Boat is not available
                    </div>
                    <div>
                      <span class="badge availability-status-maintenance">Maintenance</span> - Boat is under maintenance
                    </div>
                  </div>
                  <div class="mt-2">
                    <small class="text-muted">Click on any date cell to mark a boat's availability for that specific date.</small>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Masterlist Tab -->
          <div class="tab-pane fade <?php echo $masterlistShow; ?>" id="masterlist" role="tabpanel" aria-labelledby="masterlist-tab">
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">Boat Masterlist</h3>
                <div class="card-tools">
                  <button type="button" class="btn btn-success btn-sm" id="exportMasterlistBtn">
                    <i class="fas fa-file-export"></i> Export Masterlist
                  </button>
                  <button type="button" class="btn btn-primary btn-sm" id="printMasterlistBtn">
                    <i class="fas fa-print"></i> Print
                  </button>
                </div>
              </div>
              <div class="card-body">
                <div class="table-responsive">
                  <table id="masterlistTable" class="table table-bordered table-striped">
                    <thead>
                      <tr>
                        <th>#</th>
                        <th>Boat ID</th>
                        <th>Name</th>
                        <th>Type</th>
                        <th>Capacity</th>
                        <th>Price Per Day</th>
                        <th>Status</th>
                        <th>Assigned by Tourism Office</th>
                        <th>Description</th>
                        <th>Created Date</th>
                        <th>Last Updated</th>
                      </tr>
                    </thead>
                    <tbody>
                    <?php
                    // Get all boats with detailed information
                    $masterlistQuery = "SELECT
                        b.*,
                        CASE
                            WHEN b.assigned_by_tourism_office = 1 THEN 'Yes'
                            ELSE 'No'
                        END AS tourism_assigned,
                        (SELECT COUNT(*) FROM boat_reservations br WHERE br.boat_id = b.boat_id) AS total_bookings,
                        (SELECT MAX(updated_at) FROM boat_availability_dates bad WHERE bad.boat_id = b.boat_id) AS last_updated
                    FROM
                        boats b
                    ORDER BY
                        b.boat_id";

                    $masterlistResult = mysqli_query($con, $masterlistQuery);
                    $cnt = 1;

                    while($row = mysqli_fetch_assoc($masterlistResult)) {
                      $status = $row['status'] ?? 'Available';
                      $statusClass = '';
                      switch($status) {
                        case 'Available':
                          $statusClass = 'success';
                          break;
                        case 'Maintenance':
                          $statusClass = 'warning';
                          break;
                        case 'Not Available':
                          $statusClass = 'danger';
                          break;
                      }

                      // Format boat type for display
                      $type = $row['type'] ?? 'small';
                      $typeDisplay = ucfirst($type) . ' Boat';

                      // Format dates
                      $createdDate = !empty($row['created_at']) ? date('M d, Y', strtotime($row['created_at'])) : 'N/A';
                      $lastUpdated = !empty($row['last_updated']) ? date('M d, Y', strtotime($row['last_updated'])) : 'N/A';

                      // Check if the boat is assigned by tourism office
                      $assignedByTourismOffice = isset($row['assigned_by_tourism_office']) && $row['assigned_by_tourism_office'] == 1 ? 'Yes' : 'No';
                      $assignedClass = $assignedByTourismOffice == 'Yes' ? 'success' : 'secondary';

                      echo '<tr>
                        <td>'.$cnt++.'</td>
                        <td>'.$row['boat_id'].'</td>
                        <td>'.htmlspecialchars($row['name']).'</td>
                        <td>'.$typeDisplay.'</td>
                        <td>'.intval($row['capacity'] ?? 1).' persons</td>
                        <td>P '.number_format($row['price_per_day'], 2).'</td>
                        <td><span class="badge badge-'.$statusClass.'">'.$status.'</span></td>
                        <td><span class="badge badge-'.$assignedClass.'">'.$assignedByTourismOffice.'</span></td>
                        <td>'.htmlspecialchars(substr($row['description'] ?? 'No description', 0, 100)).(strlen($row['description'] ?? '') > 100 ? '...' : '').'</td>
                        <td>'.$createdDate.'</td>
                        <td>'.$lastUpdated.'</td>
                      </tr>';
                    }
                    ?>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
  <!-- /.content-wrapper -->
  <?php include_once('includes/footer.php');?>

  <!-- Control Sidebar -->
  <aside class="control-sidebar control-sidebar-dark">
    <!-- Control sidebar content goes here -->
  </aside>
  <!-- /.control-sidebar -->
</div>
<!-- ./wrapper -->

<!-- jQuery -->
<script src="plugins/jquery/jquery.min.js"></script>
<!-- Bootstrap 4 -->
<script src="plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
<!-- DataTables  & Plugins -->
<script src="plugins/datatables/jquery.dataTables.min.js"></script>
<script src="plugins/datatables-bs4/js/dataTables.bootstrap4.min.js"></script>
<script src="plugins/datatables-responsive/js/dataTables.responsive.min.js"></script>
<script src="plugins/datatables-responsive/js/responsive.bootstrap4.min.js"></script>
<script src="plugins/datatables-buttons/js/dataTables.buttons.min.js"></script>
<script src="plugins/datatables-buttons/js/buttons.bootstrap4.min.js"></script>
<script src="plugins/jszip/jszip.min.js"></script>
<script src="plugins/pdfmake/pdfmake.min.js"></script>
<script src="plugins/pdfmake/vfs_fonts.js"></script>
<script src="plugins/datatables-buttons/js/buttons.html5.min.js"></script>
<script src="plugins/datatables-buttons/js/buttons.print.min.js"></script>
<script src="plugins/datatables-buttons/js/buttons.colVis.min.js"></script>
<!-- SweetAlert2 -->
<script src="plugins/sweetalert2/sweetalert2.min.js"></script>
<!-- AdminLTE App -->
<script src="dist/js/adminlte.min.js"></script>
<!-- Page specific script -->
<script>
  $(function () {
    // Add New Boat button click handler
    $('#addNewBoatBtn').on('click', function() {
      // Reset the form
      $('#addBoatForm')[0].reset();
      // Show the modal
      $('#addBoatModal').modal('show');
    });

    // Save Boat button click handler
    $('#saveBoat').on('click', function() {
      // Validate the form
      var form = $('#addBoatForm');
      if (!form[0].checkValidity()) {
        form.find(':input').addClass('is-invalid');
        form.find(':input').filter(':valid').removeClass('is-invalid');
        return;
      }

      // Get form data
      var formData = form.serialize();

      // Show loading state
      var saveBtn = $(this);
      var originalText = saveBtn.text();
      saveBtn.html('<i class="fas fa-spinner fa-spin"></i> Saving...');
      saveBtn.prop('disabled', true);

      // Send AJAX request
      $.ajax({
        url: 'add-boat.php',
        type: 'POST',
        data: formData,
        dataType: 'json',
        success: function(response) {
          if (response.success) {
            // Show success message
            Swal.fire({
              icon: 'success',
              title: 'Success',
              text: 'Boat added successfully!',
              showConfirmButton: false,
              timer: 1500
            }).then(() => {
              // Close the modal
              $('#addBoatModal').modal('hide');
              // Reload the page to show the new boat
              location.reload();
            });
          } else {
            // Show error message
            Swal.fire({
              icon: 'error',
              title: 'Error',
              text: response.error || 'Failed to add boat'
            });
          }
        },
        error: function(xhr, status, error) {
          console.error('Error adding boat:', error);
          console.error('Response:', xhr.responseText);

          try {
            const errorResponse = JSON.parse(xhr.responseText);
            Swal.fire({
              icon: 'error',
              title: 'Error',
              text: errorResponse.error || 'Failed to add boat'
            });
          } catch (e) {
            Swal.fire({
              icon: 'error',
              title: 'Error',
              text: 'Failed to add boat. Please try again.'
            });
          }
        },
        complete: function() {
          // Reset button state
          saveBtn.html(originalText);
          saveBtn.prop('disabled', false);
        }
      });
    });
  });

  // Edit Boat Function
  function editBoat(boatId) {
    $.ajax({
      url: 'get_boat.php?id=' + boatId,
      type: 'GET',
      dataType: 'json', // Parse JSON automatically
      success: function(boat) {
        // Populate form fields
        $('#editBoatId').val(boatId);
        $('#editBoatName').val(boat.name);
        $('#editBoatType').val(boat.type || 'small');
        $('#editBoatCapacity').val(boat.capacity || 1);
        $('#editBoatPrice').val(boat.price_per_day);
        $('#editBoatDescription').val(boat.description || '');
        $('#editBoatStatus').val(boat.status || 'Available');
        $('#editAssignedByTourismOffice').val(boat.assigned_by_tourism_office || '0');

        // Show the modal
        $('#editBoatModal').modal('show');
      },
      error: function(xhr, status, error) {
        console.error('Error fetching boat details:', error);
        console.error('Response:', xhr.responseText);
        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: 'Failed to load boat details'
        });
      }
    });
  }

  // Save Boat Changes
  function saveBoatChanges() {
    const formData = $("#editBoatForm").serialize();

    // Validate form data
    const boatName = $('#editBoatName').val().trim();
    const boatType = $('#editBoatType').val();
    const boatCapacity = parseInt($('#editBoatCapacity').val());
    const boatPrice = parseFloat($('#editBoatPrice').val());

    // Validate required fields
    if (!boatName) {
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'Boat name is required'
      });
      return;
    }

    if (!boatType) {
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'Boat type is required'
      });
      return;
    }

    if (isNaN(boatCapacity) || boatCapacity <= 0) {
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'Please enter a valid capacity'
      });
      return;
    }

    if (isNaN(boatPrice) || boatPrice <= 0) {
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'Please enter a valid price'
      });
      return;
    }

    // Show loading state
    const saveBtn = $('button[onclick="saveBoatChanges()"]');
    const originalText = saveBtn.text();
    saveBtn.html('<i class="fas fa-spinner fa-spin"></i> Saving...');
    saveBtn.prop('disabled', true);

    $.ajax({
      url: 'update_boat.php',
      type: 'POST',
      data: formData,
      dataType: 'json',
      success: function(result) {
        if(result.success) {
          Swal.fire({
            icon: 'success',
            title: 'Success',
            text: 'Boat details updated successfully',
            showConfirmButton: false,
            timer: 1500
          }).then(() => {
            location.reload();
          });
        } else {
          Swal.fire({
            icon: 'error',
            title: 'Error',
            text: result.error || 'Failed to update boat details'
          });
        }
      },
      error: function(xhr, status, error) {
        console.error('Error updating boat:', error);
        console.error('Response:', xhr.responseText);

        try {
          const errorResponse = JSON.parse(xhr.responseText);
          Swal.fire({
            icon: 'error',
            title: 'Error',
            text: errorResponse.error || 'Failed to update boat details'
          });
        } catch (e) {
          Swal.fire({
            icon: 'error',
            title: 'Error',
            text: 'Failed to update boat details'
          });
        }
      },
      complete: function() {
        // Reset button state
        saveBtn.html(originalText);
        saveBtn.prop('disabled', false);
      }
    });
  }

  // Delete Boat Function
  function deleteBoat(boatId) {
    Swal.fire({
      title: 'Are you sure?',
      text: "You won't be able to revert this!",
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Yes, delete it!'
    }).then((result) => {
      if (result.isConfirmed) {
        $.ajax({
          url: 'delete_boat.php?id=' + boatId,
          type: 'GET',
          dataType: 'json',
          success: function(result) {
            if(result.success) {
              Swal.fire({
                icon: 'success',
                title: 'Deleted!',
                text: 'Boat has been deleted.',
                showConfirmButton: false,
                timer: 1500
              }).then(() => {
                location.reload();
              });
            } else {
              Swal.fire({
                icon: 'error',
                title: 'Error',
                text: result.error || 'Failed to delete boat'
              });
            }
          },
          error: function(xhr, status, error) {
            console.error('Error deleting boat:', error);
            console.error('Response:', xhr.responseText);

            try {
              const errorResponse = JSON.parse(xhr.responseText);
              Swal.fire({
                icon: 'error',
                title: 'Error',
                text: errorResponse.error || 'Failed to delete boat'
              });
            } catch (e) {
              Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'Failed to delete boat'
              });
            }
          }
        });
      }
    });
  }
</script>

<!-- Add Boat Modal -->
<div class="modal fade" id="addBoatModal" tabindex="-1" role="dialog" aria-labelledby="addBoatModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header bg-success">
        <h5 class="modal-title text-white" id="addBoatModalLabel">
          <i class="fas fa-ship"></i> Add New Boat
        </h5>
        <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <form id="addBoatForm">
          <div class="form-group">
            <label for="boatName">Boat Name</label>
            <input type="text" class="form-control" id="boatName" name="name" required>
          </div>
          <div class="form-group">
            <label for="boatType">Boat Type</label>
            <select class="form-control" id="boatType" name="type" required>
              <option value="">Select Boat Type</option>
              <option value="small">Small Boat</option>
              <option value="medium">Medium Boat</option>
              <option value="large">Large Boat</option>
              <option value="special">Special Boat</option>
            </select>
          </div>
          <div class="form-group">
            <label for="boatCapacity">Capacity (persons)</label>
            <input type="number" class="form-control" id="boatCapacity" name="capacity" min="1" max="50" required>
          </div>
          <div class="form-group">
            <label for="boatPrice">Price Per Day (P)</label>
            <input type="number" class="form-control" id="boatPrice" name="price_per_day" min="0" step="0.01" required>
          </div>
          <div class="form-group">
            <label for="boatDescription">Description</label>
            <textarea class="form-control" id="boatDescription" name="description" rows="3"></textarea>
          </div>
          <div class="form-group">
            <label for="boatStatus">Status</label>
            <select class="form-control" id="boatStatus" name="status" required>
              <option value="Available">Available</option>
              <option value="Maintenance">Maintenance</option>
              <option value="Not Available">Not Available</option>
            </select>
          </div>
          <div class="form-group">
            <label for="assignedByTourismOffice">Assigned by Tourism Office</label>
            <select class="form-control" id="assignedByTourismOffice" name="assigned_by_tourism_office">
              <option value="1">Yes</option>
              <option value="0" selected>No</option>
            </select>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
        <button type="button" class="btn btn-success" id="saveBoat">Save Boat</button>
      </div>
    </div>
  </div>
</div>

<!-- Edit Boat Modal -->
<div class="modal fade" id="editBoatModal" tabindex="-1" role="dialog" aria-labelledby="editBoatModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header bg-primary">
        <h5 class="modal-title text-white" id="editBoatModalLabel">
          <i class="fas fa-edit"></i> Edit Boat
        </h5>
        <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <form id="editBoatForm" method="post">
          <input type="hidden" id="editBoatId" name="editBoatId">
          <div class="form-group">
            <label for="editBoatName">Boat Name</label>
            <input type="text" class="form-control" id="editBoatName" name="editBoatName" required>
          </div>
          <div class="form-group">
            <label for="editBoatType">Boat Type</label>
            <select class="form-control" id="editBoatType" name="editBoatType" required>
              <option value="small">Small Boat</option>
              <option value="medium">Medium Boat</option>
              <option value="large">Large Boat</option>
              <option value="special">Special Boat</option>
            </select>
          </div>
          <div class="form-group">
            <label for="editBoatCapacity">Capacity (persons)</label>
            <input type="number" class="form-control" id="editBoatCapacity" name="editBoatCapacity" min="1" max="50" required>
          </div>
          <div class="form-group">
            <label for="editBoatPrice">Price Per Day (P)</label>
            <input type="number" step="0.01" min="0" class="form-control" id="editBoatPrice" name="editBoatPrice" required>
          </div>
          <div class="form-group">
            <label for="editBoatDescription">Description</label>
            <textarea class="form-control" id="editBoatDescription" name="editBoatDescription" rows="3"></textarea>
          </div>
          <div class="form-group">
            <label for="editBoatStatus">Status</label>
            <select class="form-control" id="editBoatStatus" name="editBoatStatus" required>
              <option value="Available">Available</option>
              <option value="Maintenance">Maintenance</option>
              <option value="Not Available">Not Available</option>
            </select>
          </div>
          <div class="form-group">
            <label for="editAssignedByTourismOffice">Assigned by Tourism Office</label>
            <select class="form-control" id="editAssignedByTourismOffice" name="editAssignedByTourismOffice">
              <option value="1">Yes</option>
              <option value="0">No</option>
            </select>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
        <button type="button" class="btn btn-primary" onclick="saveBoatChanges()">Save changes</button>
      </div>
    </div>
  </div>
</div>

<!-- Mark Availability Modal -->
<div class="modal fade" id="markAvailabilityModal" tabindex="-1" role="dialog" aria-labelledby="markAvailabilityModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header bg-info">
        <h5 class="modal-title text-white" id="markAvailabilityModalLabel">
          <i class="fas fa-calendar-check"></i> Mark Boat Availability
        </h5>
        <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <form id="markAvailabilityForm">
          <input type="hidden" id="availabilityBoatId" name="boat_id">

          <div class="form-group">
            <label for="availabilityBoatName">Boat</label>
            <select class="form-control" id="availabilityBoatName" name="boat_id" required>
              <option value="">-- Select Boat --</option>
              <?php
              // Reset the boats query result pointer
              $boatsQuery = $con->query("SELECT * FROM boats ORDER BY name");
              while($boat = $boatsQuery->fetch_assoc()) {
                echo '<option value="' . $boat['boat_id'] . '">' . htmlspecialchars($boat['name']) . '</option>';
              }
              ?>
            </select>
          </div>

          <div class="form-group">
            <label for="availabilityDate">Date</label>
            <input type="date" class="form-control" id="availabilityDate" name="date" min="<?php echo date('Y-m-d'); ?>" required>
          </div>

          <div class="form-group">
            <label for="availabilityStatus">Status</label>
            <select class="form-control" id="availabilityStatus" name="status" required>
              <option value="available">Available</option>
              <option value="not available">Not Available</option>
              <option value="maintenance">Maintenance</option>
            </select>
          </div>

          <div class="form-group">
            <label for="availabilityNotes">Notes</label>
            <textarea class="form-control" id="availabilityNotes" name="notes" rows="3" placeholder="Optional notes about this boat's availability"></textarea>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
        <button type="button" class="btn btn-info" id="saveAvailability">Save Availability</button>
      </div>
    </div>
  </div>
</div>

<script>
  // Mark Boat Availability Function
  function markBoatAvailability(boatId, date, status, notes) {
    // Set form values
    $('#availabilityBoatId').val(boatId);
    $('#availabilityBoatName').val(boatId);
    $('#availabilityDate').val(date);
    $('#availabilityStatus').val(status);
    $('#availabilityNotes').val(notes);

    // Update modal title
    const boatName = $('#availabilityBoatName option:selected').text();
    const formattedDate = new Date(date).toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' });
    $('#markAvailabilityModalLabel').html(`<i class="fas fa-calendar-check"></i> Mark Availability: ${boatName} - ${formattedDate}`);

    // Show the modal
    $('#markAvailabilityModal').modal('show');
  }

  $(function() {
    // Only initialize DataTable if it hasn't been initialized yet
    if (!$.fn.DataTable.isDataTable('#boatTable')) {
      $("#boatTable").DataTable({
        "responsive": true,
        "lengthChange": false,
        "autoWidth": false,
        "buttons": ["copy", "csv", "excel", "pdf", "print", "colvis"]
      }).buttons().container().appendTo('#boatTable_wrapper .col-md-6:eq(0)');
    }

    // Initialize Masterlist DataTable
    if (!$.fn.DataTable.isDataTable('#masterlistTable')) {
      var masterlistTable = $("#masterlistTable").DataTable({
        "responsive": true,
        "lengthChange": true,
        "autoWidth": false,
        "pageLength": 25,
        "buttons": ["copy", "csv", "excel", "pdf", "print", "colvis"]
      });
      masterlistTable.buttons().container().appendTo('#masterlistTable_wrapper .col-md-6:eq(0)');

      // Export Masterlist button click handler
      $('#exportMasterlistBtn').on('click', function() {
        // Trigger the Excel export button from DataTables
        masterlistTable.button('.buttons-excel').trigger();
      });

      // Print Masterlist button click handler
      $('#printMasterlistBtn').on('click', function() {
        // Trigger the Print button from DataTables
        masterlistTable.button('.buttons-print').trigger();
      });
    }

    // Add New Boat button click handler
    $('#addNewBoatBtn').on('click', function() {
      // Reset the form
      $('#addBoatForm')[0].reset();
      // Show the modal
      $('#addBoatModal').modal('show');
    });

    // Mark Availability button click handler
    $('#markAvailabilityBtn').on('click', function() {
      // Reset the form
      $('#markAvailabilityForm')[0].reset();
      // Set default date to today
      $('#availabilityDate').val(new Date().toISOString().split('T')[0]);
      // Reset modal title
      $('#markAvailabilityModalLabel').html('<i class="fas fa-calendar-check"></i> Mark Boat Availability');
      // Show the modal
      $('#markAvailabilityModal').modal('show');
    });

    // Refresh Calendar button click handler
    $('#refreshCalendarBtn').on('click', function() {
      window.location.href = 'manage-boat.php?tab=calendar';
    });

    // Tab click handlers to update URL
    $('#boat-list-tab').on('click', function() {
      // Update URL without reloading the page
      history.pushState(null, '', 'manage-boat.php?tab=boat-list');
    });

    $('#availability-calendar-tab').on('click', function() {
      // Update URL without reloading the page
      history.pushState(null, '', 'manage-boat.php?tab=calendar');
    });

    $('#masterlist-tab').on('click', function() {
      // Update URL without reloading the page
      history.pushState(null, '', 'manage-boat.php?tab=masterlist');
    });

    // Save Availability button click handler
    $('#saveAvailability').on('click', function() {
      // Validate the form
      var form = $('#markAvailabilityForm');
      if (!form[0].checkValidity()) {
        form.find(':input').addClass('is-invalid');
        form.find(':input').filter(':valid').removeClass('is-invalid');
        return;
      }

      // Get form data
      var formData = form.serialize();

      // Show loading state
      var saveBtn = $(this);
      var originalText = saveBtn.text();
      saveBtn.html('<i class="fas fa-spinner fa-spin"></i> Saving...');
      saveBtn.prop('disabled', true);

      // Send AJAX request
      $.ajax({
        url: 'mark_boat_availability.php',
        type: 'POST',
        data: formData,
        dataType: 'json',
        success: function(response) {
          if (response.success) {
            // Show success message
            Swal.fire({
              icon: 'success',
              title: 'Success',
              text: 'Boat availability updated successfully!',
              showConfirmButton: false,
              timer: 1500
            }).then(() => {
              // Close the modal
              $('#markAvailabilityModal').modal('hide');
              // Reload the page and go to calendar tab
              window.location.href = 'manage-boat.php?tab=calendar';
            });
          } else {
            // Show error message
            Swal.fire({
              icon: 'error',
              title: 'Error',
              text: response.error || 'Failed to update boat availability'
            });
          }
        },
        error: function(xhr, status, error) {
          console.error('Error updating boat availability:', error);
          console.error('Response:', xhr.responseText);

          try {
            const errorResponse = JSON.parse(xhr.responseText);
            Swal.fire({
              icon: 'error',
              title: 'Error',
              text: errorResponse.error || 'Failed to update boat availability'
            });
          } catch (e) {
            Swal.fire({
              icon: 'error',
              title: 'Error',
              text: 'Failed to update boat availability. Please try again.'
            });
          }
        },
        complete: function() {
          // Reset button state
          saveBtn.html(originalText);
          saveBtn.prop('disabled', false);
        }
      });
    });

    // Boat selection change handler
    $('#availabilityBoatName').on('change', function() {
      const boatId = $(this).val();
      const boatName = $('#availabilityBoatName option:selected').text();
      const date = $('#availabilityDate').val();

      if (boatId && date) {
        const formattedDate = new Date(date).toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' });
        $('#markAvailabilityModalLabel').html(`<i class="fas fa-calendar-check"></i> Mark Availability: ${boatName} - ${formattedDate}`);
      }
    });

    // Date selection change handler
    $('#availabilityDate').on('change', function() {
      const boatId = $('#availabilityBoatName').val();
      const boatName = $('#availabilityBoatName option:selected').text();
      const date = $(this).val();

      if (boatId && date) {
        const formattedDate = new Date(date).toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' });
        $('#markAvailabilityModalLabel').html(`<i class="fas fa-calendar-check"></i> Mark Availability: ${boatName} - ${formattedDate}`);
      }
    });

    // Save Boat button click handler
    $('#saveBoat').on('click', function() {
      // Validate the form
      var form = $('#addBoatForm');
      if (!form[0].checkValidity()) {
        form.find(':input').addClass('is-invalid');
        form.find(':input').filter(':valid').removeClass('is-invalid');
        return;
      }

      // Get form data
      var formData = form.serialize();

      // Show loading state
      var saveBtn = $(this);
      var originalText = saveBtn.text();
      saveBtn.html('<i class="fas fa-spinner fa-spin"></i> Saving...');
      saveBtn.prop('disabled', true);

      // Send AJAX request
      $.ajax({
        url: 'add-boat.php',
        type: 'POST',
        data: formData,
        dataType: 'json',
        success: function(response) {
          if (response.success) {
            // Show success message
            Swal.fire({
              icon: 'success',
              title: 'Success',
              text: 'Boat added successfully!',
              showConfirmButton: false,
              timer: 1500
            }).then(() => {
              // Close the modal
              $('#addBoatModal').modal('hide');
              // Reload the page to show the new boat
              location.reload();
            });
          } else {
            // Show error message
            Swal.fire({
              icon: 'error',
              title: 'Error',
              text: response.error || 'Failed to add boat'
            });
          }
        },
        error: function(xhr, status, error) {
          console.error('Error adding boat:', error);
          console.error('Response:', xhr.responseText);

          try {
            const errorResponse = JSON.parse(xhr.responseText);
            Swal.fire({
              icon: 'error',
              title: 'Error',
              text: errorResponse.error || 'Failed to add boat'
            });
          } catch (e) {
            Swal.fire({
              icon: 'error',
              title: 'Error',
              text: 'Failed to add boat. Please try again.'
            });
          }
        },
        complete: function() {
          // Reset button state
          saveBtn.html(originalText);
          saveBtn.prop('disabled', false);
        }
      });
    });
  });

  // Edit Boat Function
  function editBoat(boatId) {
    $.ajax({
      url: 'get_boat.php?id=' + boatId,
      type: 'GET',
      dataType: 'json', // Parse JSON automatically
      success: function(boat) {
        // Populate form fields
        $('#editBoatId').val(boatId);
        $('#editBoatName').val(boat.name);
        $('#editBoatType').val(boat.type || 'small');
        $('#editBoatCapacity').val(boat.capacity || 1);
        $('#editBoatPrice').val(boat.price_per_day);
        $('#editBoatDescription').val(boat.description || '');
        $('#editBoatStatus').val(boat.status || 'Available');

        // Show the modal
        $('#editBoatModal').modal('show');
      },
      error: function(xhr, status, error) {
        console.error('Error fetching boat details:', error);
        console.error('Response:', xhr.responseText);
        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: 'Failed to load boat details'
        });
      }
    });
  }

  // Save Boat Changes
  function saveBoatChanges() {
    const formData = $("#editBoatForm").serialize();

    // Validate form data
    const boatName = $('#editBoatName').val().trim();
    const boatType = $('#editBoatType').val();
    const boatCapacity = parseInt($('#editBoatCapacity').val());
    const boatPrice = parseFloat($('#editBoatPrice').val());

    // Validate required fields
    if (!boatName) {
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'Boat name is required'
      });
      return;
    }

    if (!boatType) {
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'Boat type is required'
      });
      return;
    }

    if (isNaN(boatCapacity) || boatCapacity <= 0) {
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'Please enter a valid capacity'
      });
      return;
    }

    if (isNaN(boatPrice) || boatPrice <= 0) {
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'Please enter a valid price'
      });
      return;
    }

    // Show loading state
    const saveBtn = $('button[onclick="saveBoatChanges()"]');
    const originalText = saveBtn.text();
    saveBtn.html('<i class="fas fa-spinner fa-spin"></i> Saving...');
    saveBtn.prop('disabled', true);

    $.ajax({
      url: 'update_boat.php',
      type: 'POST',
      data: formData,
      dataType: 'json',
      success: function(result) {
        if(result.success) {
          Swal.fire({
            icon: 'success',
            title: 'Success',
            text: 'Boat details updated successfully',
            showConfirmButton: false,
            timer: 1500
          }).then(() => {
            location.reload();
          });
        } else {
          Swal.fire({
            icon: 'error',
            title: 'Error',
            text: result.error || 'Failed to update boat details'
          });
        }
      },
      error: function(xhr, status, error) {
        console.error('Error updating boat:', error);
        console.error('Response:', xhr.responseText);

        try {
          const errorResponse = JSON.parse(xhr.responseText);
          Swal.fire({
            icon: 'error',
            title: 'Error',
            text: errorResponse.error || 'Failed to update boat details'
          });
        } catch (e) {
          Swal.fire({
            icon: 'error',
            title: 'Error',
            text: 'Failed to update boat details'
          });
        }
      },
      complete: function() {
        // Reset button state
        saveBtn.html(originalText);
        saveBtn.prop('disabled', false);
      }
    });
  }

  // Delete Boat Function
  function deleteBoat(boatId) {
    Swal.fire({
      title: 'Are you sure?',
      text: "You won't be able to revert this!",
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Yes, delete it!'
    }).then((result) => {
      if (result.isConfirmed) {
        $.ajax({
          url: 'delete_boat.php?id=' + boatId,
          type: 'GET',
          dataType: 'json',
          success: function(result) {
            if(result.success) {
              Swal.fire({
                icon: 'success',
                title: 'Deleted!',
                text: 'Boat has been deleted.',
                showConfirmButton: false,
                timer: 1500
              }).then(() => {
                location.reload();
              });
            } else {
              Swal.fire({
                icon: 'error',
                title: 'Error',
                text: result.error || 'Failed to delete boat'
              });
            }
          },
          error: function(xhr, status, error) {
            console.error('Error deleting boat:', error);
            console.error('Response:', xhr.responseText);

            try {
              const errorResponse = JSON.parse(xhr.responseText);
              Swal.fire({
                icon: 'error',
                title: 'Error',
                text: errorResponse.error || 'Failed to delete boat'
              });
            } catch (e) {
              Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'Failed to delete boat'
              });
            }
          }
        });
      }
    });
  }
</script>
</body>
</html>
<?php } ?>
