<?php
/**
 * Database Connection Configuration
 * Carles Tourism Booking System
 */

// Database configuration
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "booking_system";

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Set charset to utf8
$conn->set_charset("utf8");

// Define base URL
if (!defined('BASE_URL')) {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
    $host = $_SERVER['HTTP_HOST'];
    $path = '/Online Booking Reservation System';
    define('BASE_URL', $protocol . $host . $path . '/');
}
?>
