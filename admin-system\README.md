# Admin System

This folder contains the complete admin system for the Online Booking Reservation System, separated from the main website.

## Structure

```
admin-system/
├── login/
│   ├── admin-login.php          # Admin login page
│   └── process-admin-login.php  # Login processing
├── dashboard/
│   ├── dashboard.php            # Main admin dashboard
│   ├── includes/                # Shared includes (config, navbar, sidebar, footer)
│   ├── css/                     # Admin-specific stylesheets
│   ├── js/                      # Admin-specific JavaScript
│   ├── plugins/                 # AdminLTE plugins and libraries
│   ├── dist/                    # AdminLTE distribution files
│   ├── images/                  # Admin system images
│   └── [various admin pages]   # All admin functionality pages
└── index.php                   # Entry point (redirects to login)
```

## Access URLs

- **Admin Login**: `http://localhost/Online%20Booking%20Reservation%20System/admin-system/login/admin-login.php`
- **Admin System**: `http://localhost/Online%20Booking%20Reservation%20System/admin-system/`
- **Main Website**: `http://localhost/Online%20Booking%20Reservation%20System/php/pages/online-booking.php`

## Features

- Complete separation from main website
- All admin functionality preserved
- Updated file paths and references
- Secure login system
- Full dashboard functionality

## Notes

- The admin system is now completely independent from the main website
- All file paths have been updated to work with the new structure
- Session management and authentication remain intact
- All AdminLTE components and plugins are included
