# File Organization & Cleanup Plan

## 🗂️ Current Issues Identified:

### 1. **Duplicate Configuration Files**
- `config/` and `php/config/` folders contain similar files
- Multiple database connection files
- Scattered configuration files

### 2. **Duplicate Class Files**
- `classes/` and `php/classes/` folders
- Similar functionality in different locations

### 3. **Unused/Redundant Files**
- Multiple CSS files for similar purposes
- Backup files (.bak, .new)
- Debug/log files in multiple locations
- Unused HTML files

### 4. **Poor Naming Conventions**
- Inconsistent file naming
- Non-descriptive names
- Mixed case and special characters

## 🎯 **Reorganization Strategy**

### Phase 1: Consolidate Configuration
- Merge `config/` and `php/config/` into single `config/` folder
- Standardize database connection files
- Remove duplicate configurations

### Phase 2: Consolidate Classes
- Merge `classes/` and `php/classes/` into single `src/classes/` folder
- Remove duplicate class files
- Standardize class naming

### Phase 3: Clean CSS/JS Files
- Remove unused CSS files
- Consolidate similar stylesheets
- Organize by component/section

### Phase 4: Clean Images
- Rename images with descriptive names
- Remove unused images
- Organize by category

### Phase 5: Remove Redundant Files
- Delete backup files
- Clean up log files
- Remove unused documentation

## 📋 **Recommended New Structure**

```
carles-tourism-booking/
├── public/
│   ├── index.php (main entry point)
│   ├── assets/
│   │   ├── css/
│   │   │   ├── main.css
│   │   │   ├── components/
│   │   │   └── pages/
│   │   ├── js/
│   │   │   ├── main.js
│   │   │   ├── components/
│   │   │   └── pages/
│   │   └── images/
│   │       ├── logos/
│   │       ├── islands/
│   │       ├── beaches/
│   │       └── backgrounds/
├── src/
│   ├── classes/
│   ├── includes/
│   └── pages/
├── config/
├── admin-system/ (already organized)
├── database/
│   ├── migrations/
│   └── seeds/
├── storage/
│   └── logs/
└── docs/
```
