/* Gallery Section Styles */
.simple-gallery-section {
    padding: 60px 0;
    background: #f8f9fa;
    position: relative;
}

.simple-gallery-section .section-header {
    text-align: center;
    margin-bottom: 30px;
}

.simple-gallery-section .section-title {
    color: #333;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 10px;
}

.simple-gallery-section .section-subtitle {
    color: #666;
    font-size: 1rem;
    max-width: 700px;
    margin: 0 auto 20px;
}

.simple-gallery-filters {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 25px;
}

.simple-filter {
    background: #eee;
    color: #333;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.simple-filter.active,
.simple-filter:hover {
    background: #0f3460;
    color: white;
}

.simple-gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
}

.simple-gallery-item {
    background: white;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    transition: transform 0.2s ease;
}

.simple-gallery-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.simple-gallery-item img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.simple-gallery-info {
    padding: 15px;
}

.simple-gallery-info h3 {
    font-size: 1.1rem;
    margin: 0 0 5px;
    color: #333;
}

.simple-gallery-info p {
    margin: 0 0 5px;
    color: #666;
    font-size: 0.9rem;
}

.simple-category {
    display: inline-block;
    background: #eee;
    color: #333;
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 0.8rem;
    margin-top: 10px;
}

.simple-gallery-load-more {
    text-align: center;
    margin-top: 30px;
}

.simple-load-more-btn {
    background: #0f3460;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: background 0.2s ease;
}

.simple-load-more-btn:hover {
    background: #1b5a9d;
}

/* Responsive styles */
@media (max-width: 768px) {
    .simple-gallery-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }

    .simple-gallery-section .section-title {
        font-size: 1.8rem;
    }
}

@media (max-width: 576px) {
    .simple-gallery-grid {
        grid-template-columns: 1fr;
    }

    .simple-filter {
        padding: 6px 12px;
        font-size: 0.8rem;
    }
}
