<?php
session_start();
require_once __DIR__ . '/../../php/config/config.php';
require_once __DIR__ . '/../../php/config/connect.php';
require_once __DIR__ . '/../../classes/Database.php';
require_once __DIR__ . '/../../classes/AdminAuth.php';
require_once __DIR__ . '/../../classes/Security.php';

$error = '';
$security = new Security($pdo);
$adminAuth = new AdminAuth($pdo, $security);

// Only set $error if POST and credentials are wrong
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = trim($_POST['username'] ?? '');
    $password = trim($_POST['password'] ?? '');

    try {
        // Check if IP or username is blocked due to brute force
        if ($security->isIPBlocked($security->getClientIP())) {
            $_SESSION['login_error'] = 'Too many login attempts. Please try again later.';
            header('Location: ' . BASE_URL . 'admin-system/login/admin-login.php');
            exit;
        }

        if ($adminAuth->login($username, $password)) {
            // Clear login attempts on successful login
            $security->clearLoginAttempts($username);

            // Set login success message
            $_SESSION['login_success'] = "You have been successfully logged in.";

            header('Location: ' . BASE_URL . 'admin-system/dashboard/dashboard.php');
            exit;
        } else {
            // Log failed login attempt
            $security->logLoginAttempt($security->getClientIP(), $username, false);
            $_SESSION['login_error'] = 'Invalid username or password. Please check your credentials and try again.';
            header('Location: ' . $_SERVER['PHP_SELF']);
            exit;
        }
    } catch (Exception $e) {
        error_log("Security check error: " . $e->getMessage());
        $_SESSION['login_error'] = 'Security check failed. Please try again later.';
        header('Location: ' . BASE_URL . 'admin-system/login/admin-login.php');
        exit;
    }
}

// On GET, get error from session (if any)
if (isset($_SESSION['login_error'])) {
    $error = $_SESSION['login_error'];
    unset($_SESSION['login_error']);
}

// Check for logout message
$success_message = '';
if (isset($_SESSION['logout_message'])) {
    $success_message = $_SESSION['logout_message'];
    unset($_SESSION['logout_message']);
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:400,700&display=swap">
    <style>
        body {
            background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
            font-family: 'Roboto', sans-serif;
            height: 100vh; /* Fixed height instead of min-height */
            display: flex;
            align-items: center; /* Center vertically */
            justify-content: center; /* Center horizontally */
            flex-direction: column;
            position: relative;
            overflow: hidden; /* Hide all scrolling */
            padding: 0; /* Remove padding */
            margin: 0; /* Remove margin */
        }

        /* Logo container */
        .logo-container {
            position: absolute;
            top: 20px;
            left: 0;
            width: 100%;
            height: 150px; /* Increased height */
            display: flex;
            justify-content: center;
            align-items: center;
        }

        /* Logo at the top of the page */
        .logo-container::before {
            content: "";
            width: 150px; /* Increased width */
            height: 150px; /* Increased height */
            background-image: url('/Online Booking Reservation System/img/carleslogomunicipality.png');
            background-size: contain;
            background-position: center;
            background-repeat: no-repeat;
            opacity: 0.9; /* Increased opacity */
        }
        .header {
            text-align: center;
            margin-bottom: 0.5rem; /* Further reduced margin */
            background-color: rgba(255, 255, 255, 0.85);
            padding: 0.5rem 1.5rem; /* Further reduced padding */
            border-radius: 0.8rem;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
            position: relative;
            z-index: 1;
            margin-top: 0; /* No top margin */
            width: 90%; /* Control width */
            max-width: 350px; /* Maximum width */
        }
        .header h1 {
            font-size: 1.4rem; /* Smaller font */
            color: #1e3a8a;
            margin: 0;
            font-weight: bold;
            letter-spacing: 1px;
        }
        .header p {
            font-size: 0.9rem; /* Smaller font */
            color: #374151;
            margin: 0.2rem 0 0;
        }
        .login-container {
            background: rgba(255, 255, 255, 0.97);
            padding: 2.5rem 3rem; /* Increased padding */
            border-radius: 1.2rem;
            box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.25);
            width: 90%;
            max-width: 450px; /* Increased max-width */
            border: none;
            position: relative;
            z-index: 1;
            margin-top: 160px; /* Increased space for larger logo */
        }
        .login-container h2 {
            color: #1e3a8a;
            margin-bottom: 1.2rem; /* Increased margin */
            text-align: center;
            letter-spacing: 1px;
            font-size: 1.8rem; /* Increased font size */
        }
        .input-group {
            margin-bottom: 1.5rem; /* Increased margin */
        }
        .input-group label {
            display: block;
            color: #374151;
            margin-bottom: 0.5rem; /* Increased margin */
            font-weight: 500;
            font-size: 1.1rem; /* Increased font size */
        }
        .input-group input {
            box-sizing: border-box;
            width: 100%;
            padding: 1rem 1.2rem; /* Increased padding */
            border: 1.5px solid #cbd5e1;
            border-radius: 0.9rem;
            font-size: 1.2rem; /* Increased font size */
            background: #f5f7fa;
            transition: border 0.2s, box-shadow 0.2s;
            box-shadow: 0 2px 8px rgba(31,38,135,0.07);
            color: #22223b;
            font-family: 'Roboto', sans-serif;
            letter-spacing: 0.02em;
        }
        .input-group input:focus {
            border-color: #6366f1;
            outline: none;
            box-shadow: 0 0 0 3px #a5b4fc44;
            background: #fff;
        }
        .login-btn {
            width: 100%;
            background: linear-gradient(90deg, #6366f1 0%, #1e3a8a 100%);
            color: #fff;
            border: none;
            padding: 1rem; /* Increased padding */
            border-radius: 0.8rem;
            font-size: 1.3rem; /* Increased font size */
            font-weight: bold;
            cursor: pointer;
            transition: background 0.2s, box-shadow 0.2s;
            box-shadow: 0 2px 8px 0 rgba(99,102,241,0.08);
            margin-top: 1rem; /* Increased top margin */
        }
        .login-btn:hover {
            background: linear-gradient(90deg, #1e3a8a 0%, #6366f1 100%);
            box-shadow: 0 4px 16px 0 rgba(99,102,241,0.16);
        }
        .error-msg {
            color: #dc2626;
            margin-bottom: 1rem;
            text-align: center;
            background: #fee2e2;
            border-radius: 0.5rem;
            padding: 0.5rem 0.7rem;
            border: 1px solid #fecaca;
        }
        .success-msg {
            color: #16a34a;
            margin-bottom: 1rem;
            text-align: center;
            background: #dcfce7;
            border-radius: 0.5rem;
            padding: 0.5rem 0.7rem;
            border: 1px solid #bbf7d0;
        }
        .logo {
            display: flex;
            justify-content: center;
            margin-bottom: 1.2rem;
        }
        .logo img {
            height: 64px;
            border-radius: 12px;
            box-shadow: 0 2px 8px 0 rgba(99,102,241,0.10);
        }
        .password-wrapper {
            position: relative;
            display: flex;
            align-items: center;
        }
        .password-wrapper input[type="password"],
        .password-wrapper input[type="text"] {
            width: 100%;
        }
        .toggle-password {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            color: #a1a1aa;
            font-size: 1.15rem;
            transition: color 0.2s;
            z-index: 2;
        }
        .toggle-password:hover {
            color: #6366f1;
        }
        .footer {
            width: 100%;
            text-align: center;
            padding: 0.5rem;
            font-size: 0.8rem;
            color: rgba(255, 255, 255, 0.8);
            letter-spacing: 0.05em;
            background: rgba(0, 0, 0, 0.2);
            position: absolute;
            left: 0;
            bottom: 0;
            z-index: 10;
            user-select: none;
            font-weight: 400;
        }
        .back-home-link {
            display: inline-block;
            margin-bottom: 1.2rem;
            color: #1e3a8a;
            background: #f3f4f6;
            padding: 0.5rem 1.1rem;
            border-radius: 0.7rem;
            text-decoration: none;
            font-weight: 500;
            font-size: 1rem;
            box-shadow: 0 2px 8px 0 rgba(31,38,135,0.04);
            transition: background 0.2s, color 0.2s;
        }
        .back-home-link:hover {
            background: #e0e7ef;
            color: #6366f1;
        }
    </style>
</head>
<body>
    <!-- Logo container -->
    <div class="logo-container"></div>

    <form class="login-container" method="POST">
        <div class="logo">
            <img src="/Online Booking Reservation System/img/timbook-carles-tourism.png" alt="Logo">
        </div>
        <h2><i class="fas fa-user-shield"></i> Administrator Login</h2>
        <div style="text-align: center; margin-bottom: 1.5rem; font-size: 1.2rem; color: #4b5563;">
            <strong>Municipality of Carles</strong><br>
            Province of Iloilo
        </div>
        <?php if ($error): ?>
            <div class="error-msg"><i class="fas fa-exclamation-circle"></i> <?= htmlspecialchars($error) ?></div>
        <?php endif; ?>
        <?php if ($success_message): ?>
            <div class="success-msg"><i class="fas fa-check-circle"></i> <?= htmlspecialchars($success_message) ?></div>
        <?php endif; ?>
        <div class="input-group">
        <label for="username">Username</label>
        <input type="text" id="username" name="username" required autofocus>
        </div>
        <div class="input-group">
            <label for="password">Password</label>
            <div class="password-wrapper">
                <input type="password" id="password" name="password" required>
                <span class="toggle-password" onclick="togglePassword()"><i class="fas fa-eye" id="eyeIcon"></i></span>
            </div>
        </div>
        <button class="login-btn" type="submit">Login as Administrator</button>
        <div style="text-align:center; margin-top:1rem;">
            <a href="/Online Booking Reservation System/php/pages/online-booking.php" class="back-home-link" style="font-size:0.85rem; margin-bottom:0; padding:0.25rem 0.6rem;"> <i class="fas fa-arrow-left"></i> Back to Home</a>
        </div>
    </form>
    <script>
        function togglePassword() {
            const pwd = document.getElementById('password');
            const eye = document.getElementById('eyeIcon');
            if (pwd.type === 'password') {
                pwd.type = 'text';
                eye.classList.remove('fa-eye');
                eye.classList.add('fa-eye-slash');
            } else {
                pwd.type = 'password';
                eye.classList.remove('fa-eye-slash');
                eye.classList.add('fa-eye');
            }
        }
    </script>
    <footer class="footer">
        &copy; 2025 Online Booking Reservation Boat Rental and Accommodation for Tourists - Carles Tourism <!--Ramos-->
    </footer>
</body>
</html>


