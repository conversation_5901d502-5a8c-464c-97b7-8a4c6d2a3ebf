<?php
session_start();
include('includes/config.php');

if(isset($_SESSION['aid'])) {
    $adminid = intval($_SESSION['aid']);

    // Update last_activity to NULL to indicate offline status
    $stmt = $con->prepare("UPDATE admins SET last_activity = NULL WHERE admin_id = ?");
    $stmt->bind_param("i", $adminid);
    $stmt->execute();
    $stmt->close();

    // Clear all session variables
    $_SESSION = array();

    // Destroy the session
    session_destroy();
}

// Set a success message in session before redirecting
session_start(); // Restart session to store the message
$_SESSION['logout_message'] = "You have been successfully logged out.";

// Redirect to login page
// Use an absolute URL to ensure it works regardless of the current path
header("Location: /Online Booking Reservation System/admin-system/login/admin-login.php");
exit();
?>
