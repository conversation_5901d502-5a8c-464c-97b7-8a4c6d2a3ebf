<?php
session_start();
// Database Connection
include('includes/config.php');
// Validating Session
if(strlen($_SESSION['aid'])==0) {
  header('location:../login/admin-login.php');
  exit;
}

// Process form submission to change background
if(isset($_POST['change_background'])) {
  // Get admin details to determine role
  $admin_id = intval($_SESSION['aid']);
  $stmt = $con->prepare("SELECT role FROM admins WHERE admin_id = ?");
  $stmt->bind_param("i", $admin_id);
  $stmt->execute();
  $result = $stmt->get_result();
  $admin_data = $result->fetch_assoc();
  $user_role = ($admin_data && $admin_data['role'] === 'subadmin') ? 'subadmin' : 'admin';

  // Get color values from form
  $color1 = isset($_POST['color1']) ? $_POST['color1'] : '#3498db';
  $color2 = isset($_POST['color2']) ? $_POST['color2'] : '#2c3e50';

  // Validate hex color format
  if(!preg_match('/^#[a-f0-9]{6}$/i', $color1) || !preg_match('/^#[a-f0-9]{6}$/i', $color2)) {
    $error_msg = "Invalid color format. Please use valid hex colors.";
  } else {
    // Create directory if it doesn't exist
    $css_dir = 'css/';
    if(!file_exists($css_dir)) {
      if(!mkdir($css_dir, 0777, true)) {
        $error_msg = "Failed to create CSS directory. Please check permissions.";
      }
    }

    if(!isset($error_msg)) {
      // Update the CSS file with new colors
      $css_file = $css_dir . 'dashboard-backgrounds.css';

      // Read the current CSS file
      $css_content = file_get_contents($css_file);

      // Replace the color values for the user's role
      if($user_role === 'admin') {
        // Update admin background colors
        $css_content = preg_replace(
          '/\.admin-background\s*\{\s*background-color:[^;]+;\s*background-image:[^;]+;/',
          '.admin-background { background-color: ' . $color1 . '; background-image: linear-gradient(135deg, ' . $color1 . ' 0%, ' . $color2 . ' 100%);',
          $css_content
        );
      } else {
        // Update subadmin background colors
        $css_content = preg_replace(
          '/\.subadmin-background\s*\{\s*background-color:[^;]+;\s*background-image:[^;]+;/',
          '.subadmin-background { background-color: ' . $color1 . '; background-image: linear-gradient(135deg, ' . $color1 . ' 0%, ' . $color2 . ' 100%);',
          $css_content
        );
      }

      // Save the updated CSS
      if(file_put_contents($css_file, $css_content)) {
        $success_msg = "Background colors updated successfully.";

        // Add JavaScript to update the preview and force reload of CSS
        echo "<script>
          document.addEventListener('DOMContentLoaded', function() {
            // Update the preview
            const previewElement = document.querySelector('." . $user_role . "-preview');
            if (previewElement) {
              previewElement.style.background = 'linear-gradient(135deg, " . $color1 . " 0%, " . $color2 . " 100%)';
            }

            // Force reload of CSS by adding a timestamp parameter
            const links = document.querySelectorAll('link[rel=\"stylesheet\"]');
            links.forEach(link => {
              if (link.href.includes('dashboard-backgrounds.css')) {
                link.href = link.href + '?t=" . time() . "';
              }
            });
          });
        </script>";
      } else {
        $error_msg = "Failed to update background colors. Please check file permissions.";
      }
    }
  }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Dashboard Background Settings | Online Booking System</title>

  <!-- Google Font: Source Sans Pro -->
  <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
  <!-- Font Awesome -->
  <link rel="stylesheet" href="plugins/fontawesome-free/css/all.min.css">
  <!-- Theme style -->
  <link rel="stylesheet" href="dist/css/adminlte.min.css">
  <!-- Dashboard Backgrounds -->
  <link rel="stylesheet" href="css/dashboard-backgrounds.css">

  <style>
    .background-preview {
      width: 100%;
      height: 200px;
      background-size: cover;
      background-position: center;
      border-radius: 8px;
      margin-bottom: 20px;
      border: 1px solid #ddd;
      position: relative;
    }

    .admin-preview {
      background-color: #3498db;
      background-image: linear-gradient(135deg, #3498db 0%, #2c3e50 100%);
    }

    .subadmin-preview {
      background-color: #2ecc71;
      background-image: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
    }

    .background-preview::before {
      content: "Using gradient background";
      position: absolute;
      bottom: 10px;
      right: 10px;
      color: white;
      background: rgba(0,0,0,0.5);
      padding: 5px 10px;
      border-radius: 4px;
      font-size: 12px;
    }

    .color-option {
      display: inline-block;
      width: 30px;
      height: 30px;
      margin: 5px;
      border-radius: 50%;
      cursor: pointer;
      border: 2px solid #fff;
      box-shadow: 0 0 0 1px #ddd;
    }

    .color-option.active {
      box-shadow: 0 0 0 2px #007bff;
    }
  </style>
</head>

<?php
// Determine user role for body attribute
$admin_id = intval($_SESSION['aid']);
$stmt = $con->prepare("SELECT role FROM admins WHERE admin_id = ?");
$stmt->bind_param("i", $admin_id);
$stmt->execute();
$result = $stmt->get_result();
$admin_data = $result->fetch_assoc();
$user_role = ($admin_data && $admin_data['role'] === 'subadmin') ? 'subadmin' : 'admin';
?>
<body class="hold-transition sidebar-mini" data-user-role="<?php echo $user_role; ?>">
<div class="wrapper">
  <!-- Navbar -->
  <?php include_once('includes/navbar.php');?>
  <!-- Main Sidebar Container -->
  <?php include_once('includes/sidebar.php');?>

  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6">
            <h1>Dashboard Background Settings</h1>
          </div>
        </div>
      </div><!-- /.container-fluid -->
    </section>

    <!-- Main content -->
    <section class="content">
      <div class="container-fluid">
        <div class="row">
          <div class="col-md-6">
            <div class="card card-primary">
              <div class="card-header">
                <h3 class="card-title">Change Dashboard Background</h3>
              </div>

              <?php if(isset($error_msg)): ?>
              <div class="alert alert-danger m-3">
                <?php echo $error_msg; ?>
              </div>
              <?php endif; ?>

              <?php if(isset($success_msg)): ?>
              <div class="alert alert-success m-3">
                <?php echo $success_msg; ?>
              </div>
              <?php endif; ?>

              <form method="post">
                <div class="card-body">
                  <div class="form-group">
                    <label>Current Background</label>
                    <div class="background-preview <?php echo $user_role; ?>-preview"></div>
                  </div>

                  <div class="form-group">
                    <label>Choose Background Color</label>
                    <div class="color-options">
                      <div class="row">
                        <div class="col-12">
                          <span class="color-option active" data-color1="#3498db" data-color2="#2c3e50" style="background: linear-gradient(135deg, #3498db 0%, #2c3e50 100%);"></span>
                          <span class="color-option" data-color1="#2ecc71" data-color2="#27ae60" style="background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);"></span>
                          <span class="color-option" data-color1="#e74c3c" data-color2="#c0392b" style="background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);"></span>
                          <span class="color-option" data-color1="#9b59b6" data-color2="#8e44ad" style="background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);"></span>
                          <span class="color-option" data-color1="#f1c40f" data-color2="#f39c12" style="background: linear-gradient(135deg, #f1c40f 0%, #f39c12 100%);"></span>
                        </div>
                      </div>
                    </div>
                    <input type="hidden" name="color1" id="color1" value="#3498db">
                    <input type="hidden" name="color2" id="color2" value="#2c3e50">
                  </div>
                </div>

                <div class="card-footer">
                  <button type="submit" name="change_background" class="btn btn-primary">Update Background</button>
                </div>
              </form>
            </div>
          </div>

          <div class="col-md-6">
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">Background Information</h3>
              </div>
              <div class="card-body">
                <p>Your dashboard background is based on your user role:</p>
                <ul>
                  <li><strong>Admin:</strong> Uses the admin background image</li>
                  <li><strong>Subadmin:</strong> Uses the subadmin background image</li>
                </ul>
                <p>When you upload a new background image, it will replace the current image for your role.</p>
                <p>The background image will be visible on your dashboard and provides a personalized experience while maintaining all your access permissions.</p>

                <div class="mt-4">
                  <p><strong>Troubleshooting:</strong></p>
                  <p>If your background is not showing correctly, you can reset to the default backgrounds:</p>
                  <a href="create_default_backgrounds.php" class="btn btn-outline-primary btn-sm">
                    <i class="fas fa-sync-alt"></i> Reset to Default Backgrounds
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->

  <?php include_once('includes/footer.php');?>
</div>
<!-- ./wrapper -->

<!-- jQuery -->
<script src="plugins/jquery/jquery.min.js"></script>
<!-- Bootstrap 4 -->
<script src="plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
<!-- AdminLTE App -->
<script src="dist/js/adminlte.min.js"></script>
<!-- Dashboard Background -->
<script src="js/dashboard-background.js"></script>
<!-- bs-custom-file-input -->
<script src="plugins/bs-custom-file-input/bs-custom-file-input.min.js"></script>

<script>
$(function () {
  bsCustomFileInput.init();

  // Handle color option selection
  $('.color-option').click(function() {
    // Remove active class from all options
    $('.color-option').removeClass('active');

    // Add active class to selected option
    $(this).addClass('active');

    // Get color values from data attributes
    const color1 = $(this).data('color1');
    const color2 = $(this).data('color2');

    // Update hidden inputs
    $('#color1').val(color1);
    $('#color2').val(color2);

    // Update preview background
    const userRole = $('body').attr('data-user-role');
    $('.' + userRole + '-preview').css('background', 'linear-gradient(135deg, ' + color1 + ' 0%, ' + color2 + ' 100%)');
  });
});
</script>
</body>
</html>
