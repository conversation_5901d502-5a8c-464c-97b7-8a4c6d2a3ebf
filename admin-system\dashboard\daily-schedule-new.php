<?php
session_start();
include('includes/config.php');

if(strlen($_SESSION['aid'])==0) {
    header('location:../login/admin-login.php');
    exit();
}

// Get the selected date from GET parameter, default to today
$selected_date = isset($_GET['date']) ? $_GET['date'] : date('Y-m-d');

// Format for display
$formatted_date = date('l, F j, Y', strtotime($selected_date));

// Calculate previous and next day
$prev_day = date('Y-m-d', strtotime($selected_date . ' -1 day'));
$next_day = date('Y-m-d', strtotime($selected_date . ' +1 day'));

// Get all bookings for the selected date using the new booking_logs table
$sql = "SELECT
    bl.log_id,
    bl.booking_id,
    bl.date_of_booking,
    bl.time,
    bl.boat,
    bl.price,
    t.user_id,
    t.full_name,
    t.mobile_number,
    t.email_address,
    t.number_of_pax,
    t.tour_destination,
    t.date_of_tour,
    a.username as admin_username
FROM
    booking_logs bl
JOIN
    tourist t ON bl.user_id = t.user_id
LEFT JOIN
    admin a ON bl.admin_id = a.admin_id
WHERE
    DATE(t.date_of_tour) = ?
ORDER BY
    bl.time ASC, bl.boat ASC";

// Debug information
$debug_info = [];
$debug_info[] = "Selected date: " . $selected_date;

$stmt = $con->prepare($sql);
$stmt->bind_param("s", $selected_date);
$stmt->execute();
$result = $stmt->get_result();

// Add query debug info
$debug_info[] = "Booking logs query executed";
$debug_info[] = "Number of results: " . $result->num_rows;

// Initialize arrays to store data
$bookings = [];
$boats = [];
$boat_counts = [];
$total_tourists = 0;

// Process results
while ($row = $result->fetch_assoc()) {
    $bookings[] = $row;
    $total_tourists += $row['number_of_pax'];

    // Track unique boats
    if (!in_array($row['boat'], array_column($boats, 'boat_name'))) {
        $boats[] = [
            'boat_id' => count($boats) + 1, // Generate a sequential ID
            'boat_name' => $row['boat']
        ];
    }

    // Count bookings per boat
    if (!isset($boat_counts[$row['boat']])) {
        $boat_counts[$row['boat']] = [
            'count' => 1,
            'tourists' => $row['number_of_pax'],
            'boat_name' => $row['boat']
        ];
    } else {
        $boat_counts[$row['boat']]['count']++;
        $boat_counts[$row['boat']]['tourists'] += $row['number_of_pax'];
    }
}

// Count total boats scheduled
$total_boats = count($boats);

// If no bookings found in booking_logs, fall back to the original bookings table
if (empty($bookings)) {
    // Get all bookings for the selected date from the original bookings table
    $fallback_sql = "SELECT
        b.booking_id,
        b.booking_code,
        b.start_date,
        b.no_of_pax,
        b.booking_status,
        COALESCE(c.first_name, b.first_name) as first_name,
        COALESCE(c.last_name, b.last_name) as last_name,
        COALESCE(c.contact_number, b.contact_number) as contact_number,
        bt.boat_id,
        bt.name as boat_name,
        d.name as destination_name
    FROM
        bookings b
    LEFT JOIN
        customers c ON b.customer_id = c.customer_id
    JOIN
        boats bt ON b.boat_id = bt.boat_id
    LEFT JOIN
        destinations d ON b.destination_id = d.destination_id
    WHERE
        DATE(b.start_date) = ?
    ORDER BY
        b.start_date ASC, bt.boat_id ASC";

    $fallback_stmt = $con->prepare($fallback_sql);
    $fallback_stmt->bind_param("s", $selected_date);
    $fallback_stmt->execute();
    $fallback_result = $fallback_stmt->get_result();

    // Process fallback results
    while ($row = $fallback_result->fetch_assoc()) {
        $bookings[] = $row;
        $total_tourists += $row['no_of_pax'];

        // Track unique boats
        if (!in_array($row['boat_id'], array_column($boats, 'boat_id'))) {
            $boats[] = [
                'boat_id' => $row['boat_id'],
                'boat_name' => $row['boat_name']
            ];
        }

        // Count bookings per boat
        if (!isset($boat_counts[$row['boat_id']])) {
            $boat_counts[$row['boat_id']] = [
                'count' => 1,
                'tourists' => $row['no_of_pax'],
                'boat_name' => $row['boat_name']
            ];
        } else {
            $boat_counts[$row['boat_id']]['count']++;
            $boat_counts[$row['boat_id']]['tourists'] += $row['no_of_pax'];
        }
    }

    // Count total boats scheduled
    $total_boats = count($boats);
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Daily Schedule | Boat Booking System</title>
  <link rel="stylesheet" href="plugins/fontawesome-free/css/all.min.css">
  <link rel="stylesheet" href="dist/css/adminlte.min.css">
  <link rel="stylesheet" href="plugins/datatables-bs4/css/dataTables.bootstrap4.min.css">
  <link rel="stylesheet" href="plugins/datatables-responsive/css/responsive.bootstrap4.min.css">
  <link rel="stylesheet" href="plugins/datatables-buttons/css/buttons.bootstrap4.min.css">
  <style>
    .date-navigation {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
    }
    .date-picker {
      display: flex;
      align-items: center;
    }
    .date-picker input {
      margin-right: 10px;
    }
    .summary-box {
      background-color: #f8f9fa;
      border-radius: 5px;
      padding: 15px;
      margin-bottom: 20px;
      box-shadow: 0 0 10px rgba(0,0,0,0.1);
    }
    .summary-title {
      font-weight: bold;
      margin-bottom: 10px;
      color: #007bff;
    }
    .summary-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 5px;
    }
    .boat-card {
      background-color: #fff;
      border-radius: 5px;
      padding: 15px;
      margin-bottom: 15px;
      box-shadow: 0 0 5px rgba(0,0,0,0.1);
    }
    .boat-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
      padding-bottom: 10px;
      border-bottom: 1px solid #eee;
    }
    .boat-name {
      font-weight: bold;
      font-size: 1.1rem;
      color: #343a40;
    }
    .boat-tourists {
      background-color: #007bff;
      color: white;
      padding: 3px 8px;
      border-radius: 10px;
      font-size: 0.9rem;
    }
    .tourist-list {
      list-style: none;
      padding: 0;
      margin: 0;
    }
    .tourist-item {
      padding: 8px 0;
      border-bottom: 1px solid #f0f0f0;
    }
    .tourist-item:last-child {
      border-bottom: none;
    }
    .status-badge {
      float: right;
    }
    @media print {
      .no-print {
        display: none;
      }
      .print-only {
        display: block !important;
      }
      .card {
        box-shadow: none !important;
        border: 1px solid #ddd;
      }
      .boat-card {
        break-inside: avoid;
      }
    }
    .print-only {
      display: none;
    }
  </style>
</head>
<body class="hold-transition sidebar-mini">
<div class="wrapper">
  <?php include_once('includes/navbar.php'); ?>
  <?php include_once('includes/sidebar.php'); ?>

  <div class="content-wrapper">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6">
            <h1>Daily Schedule</h1>
          </div>
        </div>
      </div>
    </section>

    <section class="content">
      <div class="container-fluid">
        <!-- Date Navigation -->
        <div class="card no-print">
          <div class="card-body">
            <div class="date-navigation">
              <a href="?date=<?php echo $prev_day; ?>" class="btn btn-default">
                <i class="fas fa-chevron-left"></i> Previous Day
              </a>

              <div class="date-picker">
                <form action="" method="get" id="dateForm">
                  <div class="input-group">
                    <input type="date" name="date" id="datePicker" class="form-control" value="<?php echo $selected_date; ?>">
                    <div class="input-group-append">
                      <button type="submit" class="btn btn-primary">Go</button>
                    </div>
                  </div>
                </form>
              </div>

              <a href="?date=<?php echo $next_day; ?>" class="btn btn-default">
                Next Day <i class="fas fa-chevron-right"></i>
              </a>
            </div>
          </div>
        </div>

        <!-- Print Header (only visible when printing) -->
        <div class="print-only">
          <h2 class="text-center">Daily Schedule</h2>
          <h3 class="text-center"><?php echo $formatted_date; ?></h3>
          <hr>
        </div>

        <!-- Schedule Summary -->
        <div class="row">
          <div class="col-md-12">
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">
                  <i class="fas fa-calendar-day"></i> Schedule for <?php echo $formatted_date; ?>
                </h3>
                <div class="card-tools">
                  <button type="button" class="btn btn-tool" onclick="window.print()">
                    <i class="fas fa-print"></i> Print
                  </button>
                </div>
              </div>
              <div class="card-body">
                <!-- Summary Section -->
                <div class="row">
                  <div class="col-md-6">
                    <div class="summary-box">
                      <div class="summary-title">Schedule Summary</div>
                      <div class="summary-item">
                        <span>Total Bookings:</span>
                        <span><strong><?php echo count($bookings); ?></strong></span>
                      </div>
                      <div class="summary-item">
                        <span>Total Tourists:</span>
                        <span><strong><?php echo $total_tourists; ?></strong></span>
                      </div>
                      <div class="summary-item">
                        <span>Total Boats Scheduled:</span>
                        <span><strong><?php echo $total_boats; ?></strong></span>
                      </div>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="summary-box">
                      <div class="summary-title">Boats Summary</div>
                      <?php if (empty($boat_counts)): ?>
                        <div class="text-muted">No boats scheduled for this day.</div>
                      <?php else: ?>
                        <?php foreach ($boat_counts as $boat_id => $data): ?>
                          <div class="summary-item">
                            <span><?php echo htmlspecialchars($data['boat_name']); ?>:</span>
                            <span><strong><?php echo $data['tourists']; ?> tourists</strong></span>
                          </div>
                        <?php endforeach; ?>
                      <?php endif; ?>
                    </div>
                  </div>
                </div>

                <!-- Detailed Schedule Section -->
                <h4 class="mt-4">Detailed Schedule</h4>

                <?php if (empty($bookings)): ?>
                  <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> No bookings scheduled for this day.
                  </div>
                <?php else: ?>
                  <!-- Group bookings by boat -->
                  <?php
                  $bookings_by_boat = [];
                  foreach ($bookings as $booking) {
                      // Check if we're using the new booking_logs data or fallback data
                      if (isset($booking['boat'])) {
                          // New booking_logs data
                          $boat_key = $booking['boat'];
                          $boat_name = $booking['boat'];
                          $tourist_name = $booking['full_name'];
                          $tourist_contact = $booking['mobile_number'];
                          $tourist_count = $booking['number_of_pax'];
                          $destination = $booking['tour_destination'];
                          $booking_code = 'BL-' . $booking['log_id']; // Create a booking code from log_id
                          $status = 'confirmed'; // Default status for booking_logs
                      } else {
                          // Fallback to original bookings data
                          $boat_key = $booking['boat_id'];
                          $boat_name = $booking['boat_name'];
                          $tourist_name = $booking['first_name'] . ' ' . $booking['last_name'];
                          $tourist_contact = $booking['contact_number'];
                          $tourist_count = $booking['no_of_pax'];
                          $destination = $booking['destination_name'];
                          $booking_code = $booking['booking_code'];
                          $status = $booking['booking_status'];
                      }

                      if (!isset($bookings_by_boat[$boat_key])) {
                          $bookings_by_boat[$boat_key] = [
                              'boat_name' => $boat_name,
                              'bookings' => []
                          ];
                      }

                      $bookings_by_boat[$boat_key]['bookings'][] = [
                          'tourist_name' => $tourist_name,
                          'tourist_contact' => $tourist_contact,
                          'tourist_count' => $tourist_count,
                          'destination' => $destination,
                          'booking_code' => $booking_code,
                          'status' => $status
                      ];
                  }
                  ?>

                  <div class="row">
                    <?php foreach ($bookings_by_boat as $boat_key => $data): ?>
                      <div class="col-md-6">
                        <div class="boat-card">
                          <div class="boat-header">
                            <div class="boat-name">
                              <i class="fas fa-ship"></i> <?php echo htmlspecialchars($data['boat_name']); ?>
                            </div>
                            <div class="boat-tourists">
                              <?php
                              $boat_tourist_count = 0;
                              foreach ($data['bookings'] as $booking) {
                                  $boat_tourist_count += $booking['tourist_count'];
                              }
                              echo $boat_tourist_count . ' tourists';
                              ?>
                            </div>
                          </div>

                          <ul class="tourist-list">
                            <?php foreach ($data['bookings'] as $booking): ?>
                              <?php
                              // Determine status class
                              $status_class = '';
                              $status_text = ucfirst($booking['status']);

                              switch ($booking['status']) {
                                  case 'confirmed':
                                  case 'accepted':
                                      $status_class = 'badge-success';
                                      break;
                                  case 'pending':
                                      $status_class = 'badge-warning';
                                      break;
                                  case 'cancelled':
                                  case 'rejected':
                                      $status_class = 'badge-danger';
                                      break;
                                  default:
                                      $status_class = 'badge-secondary';
                              }
                              ?>
                              <li class="tourist-item">
                                <div>
                                  <strong><?php echo htmlspecialchars($booking['tourist_name']); ?></strong>
                                  <span class="badge <?php echo $status_class; ?> status-badge"><?php echo $status_text; ?></span>
                                </div>
                                <div>
                                  <small>
                                    <i class="fas fa-users"></i> <?php echo $booking['tourist_count']; ?> pax |
                                    <i class="fas fa-phone"></i> <?php echo htmlspecialchars($booking['tourist_contact']); ?> |
                                    <i class="fas fa-map-marker-alt"></i> <?php echo htmlspecialchars($booking['destination']); ?>
                                  </small>
                                </div>
                                <div>
                                  <small>
                                    <i class="fas fa-ticket-alt"></i> <?php echo htmlspecialchars($booking['booking_code']); ?>
                                  </small>
                                </div>
                              </li>
                            <?php endforeach; ?>
                          </ul>
                        </div>
                      </div>
                    <?php endforeach; ?>
                  </div>
                <?php endif; ?>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>

  <?php include_once('includes/footer.php'); ?>
</div>

<script src="plugins/jquery/jquery.min.js"></script>
<script src="plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
<script src="dist/js/adminlte.min.js"></script>
<script>
$(function () {
  // Auto-submit when date changes
  $('#datePicker').change(function() {
    $('#dateForm').submit();
  });
});
</script>
</body>
</html>
